@extends('layouts.master')

@section('page.title', 'Đăng nhập - Alan Company')

@section('page.styles')
    <link rel="stylesheet" href="{{ asset('css/modern-login.css') }}?v={{ time() }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endsection

@section('page.body.class', 'modern-login-page')

@section('page.body')
    <div class="login-container">
        <!-- Left Side - Branding & Visual -->
        <div class="login-left">
            <div class="brand-section">
                <div class="brand-logo">
                    <img src="{{ asset_with_version('img/alan-logo.png') }}" alt="Alan Company Logo" class="logo-img">
                </div>
                <h1 class="brand-title">Alan Company</h1>
                <p class="brand-subtitle"><PERSON><PERSON> thống quản lý doanh nghiệp hiện đại</p>
            </div>

            <div class="visual-elements">
                <div class="floating-card card-1">
                    <i class="fas fa-chart-line"></i>
                    <span>Báo cáo thống kê</span>
                </div>
                <div class="floating-card card-2">
                    <i class="fas fa-users"></i>
                    <span>Quản lý khách hàng</span>
                </div>
                <div class="floating-card card-3">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Lịch hẹn</span>
                </div>
            </div>

            <div class="background-pattern"></div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="login-right">
            <div class="login-form-container">
                <div class="login-header">
                    <h2 class="login-title">Chào mừng trở lại!</h2>
                    <p class="login-subtitle">Đăng nhập để tiếp tục làm việc</p>
                </div>

                <form action="{{ route('auth.login') }}" method="post" class="modern-form">
                    @csrf

                    <!-- Account Input -->
                    <div class="input-group @error('account') error @enderror">
                        <div class="input-wrapper">
                            <i class="fas fa-user input-icon"></i>
                            <input type="text"
                                   name="account"
                                   id="account"
                                   class="modern-input"
                                   value="{{ old('account') }}"
                                   required>
                            <label for="account" class="floating-label">Tài khoản</label>
                            <div class="input-border"></div>
                        </div>
                        @error('account')
                            <span class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {{ $message }}
                            </span>
                        @enderror
                    </div>

                    <!-- Password Input -->
                    <div class="input-group @error('password') error @enderror">
                        <div class="input-wrapper">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password"
                                   name="password"
                                   id="password"
                                   class="modern-input"
                                   required>
                            <label for="password" class="floating-label">Mật khẩu</label>
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="fas fa-eye" id="password-eye"></i>
                            </button>
                            <div class="input-border"></div>
                        </div>
                        @error('password')
                            <span class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {{ $message }}
                            </span>
                        @enderror
                    </div>

                    <!-- Remember Me -->
                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" name="remember">
                            <span class="checkmark"></span>
                            <span class="checkbox-text">Ghi nhớ đăng nhập</span>
                        </label>
                        <a href="#" class="forgot-password">Quên mật khẩu?</a>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="login-btn">
                        <span class="btn-text">Đăng nhập</span>
                        <div class="btn-loader">
                            <div class="spinner"></div>
                        </div>
                        <i class="fas fa-arrow-right btn-icon"></i>
                    </button>
                </form>

                <!-- Footer -->
                <div class="login-footer">
                    <p>&copy; {{ date('Y') }} Alan Company. All rights reserved.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Background Animation -->
    <div class="bg-animation">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>
@endsection

@section('page.scripts')
    <script src="{{ asset('js/modern-login.js') }}?v={{ time() }}"></script>
@endsection
