@extends('layouts.master')

@section('page.title', 'Đ<PERSON>ng nhập - Alan Company')

@section('page.styles')
    <link rel="stylesheet" href="{{ asset('css/modern-login.css') }}?v={{ time() }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endsection

@section('page.body.class', 'modern-login-page')

@section('page.body')
    <div class="login-container">
        <!-- Left Side - Login Form -->
        <div class="login-left">
            <div class="login-form-container">
                <div class="login-header">
                    <div class="welcome-icon">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <h2 class="login-title"><PERSON><PERSON><PERSON> nhậ<PERSON> hệ thống</h2>
                    <p class="login-subtitle"><PERSON><PERSON> lòng nhập thông tin để tiếp tục</p>
                </div>

                <form action="{{ route('auth.login') }}" method="post" class="modern-form">
                    @csrf

                    <!-- Account Input -->
                    <div class="input-group @error('account') error @enderror">
                        <div class="input-wrapper">
                            <input type="text"
                                   name="account"
                                   id="account"
                                   class="modern-input"
                                   value="{{ old('account') }}"
                                   placeholder=" "
                                   required>
                            <label for="account" class="floating-label">
                                <i class="fas fa-user"></i>
                                <span>Tài khoản</span>
                            </label>
                            <div class="input-focus-border"></div>
                        </div>
                        @error('account')
                            <span class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {{ $message }}
                            </span>
                        @enderror
                    </div>

                    <!-- Password Input -->
                    <div class="input-group @error('password') error @enderror">
                        <div class="input-wrapper">
                            <input type="password"
                                   name="password"
                                   id="password"
                                   class="modern-input"
                                   placeholder=" "
                                   required>
                            <label for="password" class="floating-label">
                                <i class="fas fa-lock"></i>
                                <span>Mật khẩu</span>
                            </label>
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="fas fa-eye" id="password-eye"></i>
                            </button>
                            <div class="input-focus-border"></div>
                        </div>
                        @error('password')
                            <span class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {{ $message }}
                            </span>
                        @enderror
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="login-btn">
                        <span class="btn-text">Đăng nhập</span>
                        <div class="btn-loader">
                            <div class="spinner"></div>
                        </div>
                        <i class="fas fa-arrow-right btn-icon"></i>
                    </button>
                </form>

                <!-- Footer -->
                <div class="login-footer">
                    <p>&copy; {{ date('Y') }} Alan Company. All rights reserved.</p>
                </div>
            </div>
        </div>

        <!-- Right Side - Branding & Visual -->
        <div class="login-right">
            <div class="brand-section">
                <div class="brand-logo">
                    <img src="{{ asset_with_version('img/alan-logo.png') }}" alt="Alan Company Logo" class="logo-img">
                </div>
                <h1 class="brand-title">Alan Company</h1>
                <div class="brand-slogan">
                    <p class="slogan-main">Giải pháp quản lý toàn diện</p>
                    <p class="slogan-sub">Nâng tầm doanh nghiệp với công nghệ hiện đại</p>
                </div>
            </div>

            <div class="visual-elements">
                <div class="floating-card card-1">
                    <div class="card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="card-content">
                        <h4>Báo cáo thống kê</h4>
                        <p>Phân tích dữ liệu chi tiết</p>
                    </div>
                </div>
                <div class="floating-card card-2">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="card-content">
                        <h4>Quản lý khách hàng</h4>
                        <p>CRM chuyên nghiệp</p>
                    </div>
                </div>
                <div class="floating-card card-3">
                    <div class="card-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="card-content">
                        <h4>Lịch hẹn thông minh</h4>
                        <p>Tự động hóa quy trình</p>
                    </div>
                </div>
            </div>

            <div class="background-pattern"></div>
        </div>
    </div>

    <!-- Background Animation -->
    <div class="bg-animation">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>
@endsection

@section('page.scripts')
    <script src="{{ asset('js/modern-login.js') }}?v={{ time() }}"></script>
@endsection
