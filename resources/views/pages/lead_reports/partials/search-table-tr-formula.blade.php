@php
    use App\Constants\LeadReport\LeadReportTableConstants;
    $headerStyles = $headerStyles ?? [];
@endphp

@foreach(LeadReportTableConstants::HEADERS as $index => $header)
    <th @if(!empty($reportsData['prev_range_time'])) colspan="{{ $reportsData['prev_range_time']['count'] + 1 }}" @endif
        style="min-width: {{ $headerStyles[$index]['min-width'] ?? '100px' }}; 
               padding: {{ $headerStyles[$index]['padding'] ?? '8px 12px' }}; 
               white-space: {{ $headerStyles[$index]['white-space'] ?? 'nowrap' }}; 
               max-width: {{ $headerStyles[$index]['max-width'] ?? 'none' }};">
        <p class="footer-header-table">
            @if($index === 17)
                @php
                    $departmentIds = request('department_ids', []);
                    $isDept6Selected = in_array('6', $departmentIds) || in_array(6, $departmentIds);
                @endphp

                @if($isDept6Selected && count($departmentIds) == 1)
                    {{-- Only department 6 is selected --}}
                    10 = 9*{{ config('common.account_fee_ratio_dept6_new') * 100 }}% <br>
                    <small style="font-size: 9px"><i>(Trước tháng 05/2025 là {{ config('common.account_fee_ratio_dept6_old') * 100 }}%)</i></small>
                @elseif(!$isDept6Selected && count($departmentIds) > 0)
                    {{-- Only other departments are selected (not including dept 6) --}}
                    10 = 9*{{ config('common.account_fee_ratio_others_new') * 100 }}% <br>
                    <small style="font-size: 9px"><i>(Trước tháng 01/2025 là {{ config('common.account_fee_ratio_others_old') * 100 }}%)</i></small>
                @else
                    {{-- Mixed departments or no specific department selected --}}
                    10 = 9*{{ config('common.account_fee_ratio_others_new') * 100 }}% <br>
                    <small style="font-size: 9px"><i>(Trước tháng 01/2025 là {{ config('common.account_fee_ratio_others_old') * 100 }}%)</i></small>
                @endif
            @else
                {{ LeadReportTableConstants::FORMULAS[$index] }}
            @endif
        </p>
    </th>
@endforeach
