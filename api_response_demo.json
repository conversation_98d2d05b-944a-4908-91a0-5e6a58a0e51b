{"success": true, "message": "API đã đư<PERSON><PERSON> tối ưu cho TikTok ADS mới và Traditional ADS cũ", "data": {"headers": ["STT", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>h thu", "<PERSON>ổng chi phí / <PERSON><PERSON><PERSON> thu (%)", "Tỉ lệ khách thực qua / SĐT (%)", "Tỉ lệ đặt lịch trong ng<PERSON> (%)", "Tỉ lệ đặt lịch trong tháng (%)", "<PERSON><PERSON><PERSON> thu KH mới", "<PERSON><PERSON><PERSON> thu KH cũ", "<PERSON><PERSON>h thu KH khác", "<PERSON><PERSON>a đơn / KH", "<PERSON><PERSON><PERSON> đơn/ khách mới", "Doanh thu / SĐT", "Doanh thu KH mới / SĐT", "Tổng chi phí", "Chi phí thuê group", "<PERSON><PERSON>", "Chi phí thuê TK", "Chi phí phòng ND", "Tổng IB", "Tổng SĐT", "Tổng SĐT nội thành", "Tổng SĐT tỉnh", "Tỷ lệ SĐT tỉnh (%)", "Tổng SĐT tiếp cận", "Tỷ lệ tiếp cận (%)", "Chi phí / IB", "Chi phí / Số", "Tỉ lệ xin số (%)", "<PERSON><PERSON>ch đặt trong ngày", "Tỉ lệ đặt lịch trong ng<PERSON> (%)", "<PERSON><PERSON><PERSON> đặt trong tháng", "Tỉ lệ đặt lịch trong tháng (%)", "Chi phí / lịch đặt trong ngày", "Chi phí / lịch đặt trong tháng", "<PERSON><PERSON><PERSON><PERSON> mới", "<PERSON><PERSON><PERSON><PERSON> vãng lai", "Khá<PERSON> fail", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> fail + cọc", "<PERSON><PERSON><PERSON><PERSON> CS từ chối điều trị", "Tỉ lệ fail tại CS (%)", "Tỉ lệ fail+cọc tại <PERSON> (%)", "Tỉ lệ khách thực qua / SĐT (%)", "Tỉ lệ đặt lịch trong ng<PERSON> (%)", "Tỉ l<PERSON> khách thực qua / <PERSON><PERSON><PERSON> chốt (%)", "Chi phí MKT cho mỗi KH đến CS", "Chi phí MKT cho mỗi KH đến CS và thực hiện dịch vụ", "<PERSON><PERSON><PERSON> h<PERSON>a đơn mỗi KHM đến CS và thực hiện dịch vụ", "<PERSON><PERSON><PERSON> h<PERSON>a đ<PERSON>n kh<PERSON>ch mới", "<PERSON><PERSON><PERSON> thu kh<PERSON>ch cũ (%)", "<PERSON><PERSON><PERSON> qu<PERSON> dịch v<PERSON> của b<PERSON>c sĩ", "<PERSON><PERSON><PERSON><PERSON> độ phục vụ của EKIP"], "headerStyles": [{"width": 60, "minWidth": 60, "textAlign": "center", "whiteSpace": "nowrap", "fontWeight": "bold"}, {"width": 120, "minWidth": 120, "textAlign": "center", "whiteSpace": "nowrap", "fontWeight": "bold"}, {"width": 140, "minWidth": 140, "textAlign": "right", "whiteSpace": "nowrap", "fontWeight": "bold"}, {"width": 180, "minWidth": 180, "textAlign": "center", "whiteSpace": "normal", "fontWeight": "bold", "wordWrap": "break-word"}, {"width": 200, "minWidth": 200, "textAlign": "center", "whiteSpace": "normal", "fontWeight": "bold", "wordWrap": "break-word"}], "formulas": {"traditional_ads": ["", "", "1=3+4+5", "2=7/1", "32=24/13", "20=19/13", "22=21/13", "3", "4", "5", "6=1/24", "6.0=3/24", "6.1=1/13", "6.2=3/13", "7=8+9+10+11\n(TH Seeding: 7=(8+9+10+11)*1.5)", "8", "9", "10 = 9*3%\n(<PERSON><PERSON><PERSON><PERSON><PERSON> tháng 01/2025 là 2%)", "11"], "tiktok_ads": ["", "", "1=3+4+5", "2=7/1", "32=24/13", "20=19/13", "22=21/13", "3", "4", "5", "6=1/24", "6.0=3/24", "6.1=1/13", "6.2=3/13", "7=8+9+10+11\n(TH Seeding: 7=(8+9+10+11)*1.5)", "8", "9", "10 = 9*1%\n(<PERSON><PERSON><PERSON><PERSON><PERSON> tháng 05/2025 là 3%)", "11"]}, "widthOfColumns": [60, 100, 120, 100, 120, 100, 100, 120, 120, 120, 120, 120, 120, 120, 120, 120, 120, 120, 120, 100, 100, 100, 100, 100, 100, 100, 120, 120, 100, 100, 100, 100, 100, 120, 120, 100, 100, 100, 100, 100, 100, 100, 100, 120, 100, 130, 120, 200, 200, 100, 100, 100, 100], "totals": ["TỔNG", "", "1,500,000,000", "15.5%", "25.3%"], "dayAvg": ["TB Ngày", "", "50,000,000", "12.8%", "22.1%"], "last3MonthsAvg": ["TB 3T", "", "45,000,000", "14.2%", "23.7%"], "rows": [[1, "2024-01-01", "100,000,000", "12.5%", "20.5%"], [2, "2024-01-02", "120,000,000", "13.2%", "22.1%"], [3, "2024-01-03", "95,000,000", "11.8%", "19.8%"]]}}