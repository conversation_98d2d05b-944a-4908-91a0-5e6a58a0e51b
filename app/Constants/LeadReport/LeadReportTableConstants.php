<?php

namespace App\Constants\LeadReport;

class LeadReportTableConstants
{
    public const HEADERS = [
        "STT",
        "Ngà<PERSON> nhập",
        "<PERSON><PERSON><PERSON> thu",
        "Tổng chi phí / <PERSON><PERSON><PERSON> thu (%)",
        "Tỉ lệ khách thực qua / SĐT (%)",
        "Tỉ lệ đặt lịch trong ngày (%)",
        "Tỉ lệ đặt lịch trong tháng (%)",
        "Doanh thu KH mới",
        "Doanh thu KH cũ",
        "Doanh thu KH khác",
        "<PERSON><PERSON>a đơn / KH",
        "<PERSON><PERSON>a đơn/ khách mới",
        "Do<PERSON><PERSON> thu / SĐT",
        "Doanh thu KH mới / SĐT",
        "Tổng chi phí",
        "Chi phí thuê group",
        "Ngân sách",
        "Chi phí thuê TK",
        "Chi phí phòng ND",
        "Tổng IB",
        "Tổng SĐT",
        "Tổng SĐT nội thành",
        "Tổng SĐT tỉnh",
        "Tỷ lệ SĐT tỉnh (%)",
        "Tổng SĐT tiếp cận",
        "Tỷ lệ tiếp cận (%)",
        "Chi phí / IB",
        "Chi phí / Số",
        "Tỉ lệ xin số (%)",
        "Lịch đặt trong ngày",
        "Tỉ lệ đặt lịch trong ngày (%)",
        "Lịch đặt trong tháng",
        "Tỉ lệ đặt lịch trong tháng (%)",
        "Chi phí / lịch đặt trong ngày",
        "Chi phí / lịch đặt trong tháng",
        "Khách mới",
        "Khách vãng lai",
        "Khách fail",
        "Khách cọc",
        "Khách fail + cọc",
        "Khách CS từ chối điều trị",
        "Tỉ lệ fail tại CS (%)",
        "Tỉ lệ fail+cọc tại CS (%)",
        "Tỉ lệ khách thực qua / SĐT (%)",
        "Tỉ lệ đặt lịch trong ngày (%)",
        "Tỉ lệ khách thực qua / Lịch chốt (%)",
        "Chi phí MKT cho mỗi KH đến CS",
        "Chi phí MKT cho mỗi KH đến CS và thực hiện dịch vụ",
        "Đầu hóa đơn mỗi KHM đến CS và thực hiện dịch vụ",
        "Đầu hóa đơn khách mới",
        "Doanh thu khách cũ (%)",
        "Hiệu quả dịch vụ của bác sĩ",
        "Thái độ phục vụ của EKIP",
    ];

    public const HEADER_STYLES = [
        'STT' => ['min-width' => '60px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'REPORT_DATE' => ['min-width' => '100px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'REVENUE' => ['min-width' => '120px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'FEE_REVENUE_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'NEW_CUSTOMER_PHONE_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'DATE_BOOKING_PHONE_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'MONTH_BOOKING_PHONE_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'NEW_CUSTOMER_REVENUE' => ['min-width' => '120px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'OLD_CUSTOMER_REVENUE' => ['min-width' => '120px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'OTHER_CUSTOMER_REVENUE' => ['min-width' => '120px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'INVOICE_PER_CUSTOMER' => ['min-width' => '120px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'INVOICE_PER_NEW_CUSTOMER' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'REVENUE_PER_PHONE' => ['min-width' => '120px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'NEW_CUSTOMER_REVENUE_PER_PHONE' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'TOTAL_FEE' => ['min-width' => '120px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'GROUP_FEE' => ['min-width' => '120px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'BUDGET' => ['min-width' => '120px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'ACCOUNT_FEE' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'CONTENT_DEPARTMENT_FEE' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'TOTAL_IB' => ['min-width' => '100px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'TOTAL_PHONE' => ['min-width' => '100px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'TOTAL_INNER_PHONE' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'TOTAL_PROVINCE_PHONE' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'PROVINCE_PHONE_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'TOTAL_CONTACTED_PHONE' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'CONTACT_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'FEE_PER_IB' => ['min-width' => '120px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'FEE_PER_PHONE' => ['min-width' => '120px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'PHONE_REQUEST_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'DATE_BOOKING_COUNT' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'DATE_BOOKING_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'MONTH_BOOKING_COUNT' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'MONTH_BOOKING_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'FEE_PER_DATE_BOOKING' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'FEE_PER_MONTH_BOOKING' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'NEW_CUSTOMER_COUNT' => ['min-width' => '100px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'RANDOM_CUSTOMER_COUNT' => ['min-width' => '100px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'FAILED_CUSTOMER_COUNT' => ['min-width' => '100px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'DEPOSIT_CUSTOMER_COUNT' => ['min-width' => '100px', 'padding' => '8px 12px', 'white-space' => 'nowrap'],
        'FAILED_DEPOSIT_CUSTOMER_COUNT' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'REJECTED_CUSTOMER_COUNT' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'FAILED_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'FAILED_DEPOSIT_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'NEW_CUSTOMER_PHONE_RATIO_2' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'DATE_BOOKING_PHONE_RATIO_2' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'NEW_CUSTOMER_BOOKING_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'FEE_PER_NEW_CUSTOMER' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'FEE_PER_SERVICED_CUSTOMER' => ['min-width' => '200px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '250px'],
        'INVOICE_PER_SERVICED_CUSTOMER' => ['min-width' => '200px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '250px'],
        'INVOICE_PER_NEW_CUSTOMER_2' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'OLD_CUSTOMER_REVENUE_RATIO' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'DOCTOR_EFFECTIVENESS' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
        'SERVICE_ATTITUDE' => ['min-width' => '150px', 'padding' => '8px 12px', 'white-space' => 'normal', 'max-width' => '200px'],
    ];

    public const FORMULA_KEYS = [
        'STT' => 0,
        'REPORT_DATE' => 1,
        'REVENUE' => 2,
        'FEE_REVENUE_RATIO' => 3,
        'NEW_CUSTOMER_PHONE_RATIO' => 4,
        'DATE_BOOKING_PHONE_RATIO' => 5,
        'MONTH_BOOKING_PHONE_RATIO' => 6,
        'NEW_CUSTOMER_REVENUE' => 7,
        'OLD_CUSTOMER_REVENUE' => 8,
        'OTHER_CUSTOMER_REVENUE' => 9,
        'INVOICE_PER_CUSTOMER' => 10,
        'INVOICE_PER_NEW_CUSTOMER' => 11,
        'REVENUE_PER_PHONE' => 12,
        'NEW_CUSTOMER_REVENUE_PER_PHONE' => 13,
        'TOTAL_FEE' => 14,
        'GROUP_FEE' => 15,
        'BUDGET' => 16,
        'ACCOUNT_FEE' => 17,
        'CONTENT_DEPARTMENT_FEE' => 18,
    ];

    public const STATIC_FORMULAS = [
        'STT' => '',
        'REPORT_DATE' => '',
        'REVENUE' => '1=3+4+5',
        'FEE_REVENUE_RATIO' => '2=7/1',
        'NEW_CUSTOMER_PHONE_RATIO' => '32=24/13',
        'DATE_BOOKING_PHONE_RATIO' => '20=19/13',
        'MONTH_BOOKING_PHONE_RATIO' => '22=21/13',
        'NEW_CUSTOMER_REVENUE' => '3',
        'OLD_CUSTOMER_REVENUE' => '4',
        'OTHER_CUSTOMER_REVENUE' => '5',
        'INVOICE_PER_CUSTOMER' => '6=1/24',
        'INVOICE_PER_NEW_CUSTOMER' => '6.0=3/24',
        'REVENUE_PER_PHONE' => '6.1=1/13',
        'NEW_CUSTOMER_REVENUE_PER_PHONE' => '6.2=3/13',
        'TOTAL_FEE' => "7=8+9+10+11\n(TH Seeding: 7=(8+9+10+11)*1.5)",
        'GROUP_FEE' => '8',
        'BUDGET' => '9',
        'CONTENT_DEPARTMENT_FEE' => '11',
    ];

    public const WIDTH_OF_COLUMNS = [
        60, // STT
        100, // Ngày nhập
        120, // Doanh thu
        100, // Tổng chi phí / Doanh thu (%)
        120, // Tỉ lệ khách thực qua / SĐT (%)
        100, // Tỉ lệ đặt lịch trong ngày (%)
        100, // Tỉ lệ đặt lịch trong tháng (%)
        120, // Doanh thu KH mới
        120, // Doanh thu KH cũ
        120, // Doanh thu KH khác
        120, // Hóa đơn / KH
        120, // Hóa đơn/ khách mới
        120, // Doanh thu / SĐT
        120, // Doanh thu KH mới / SĐT
        120, // Tổng chi phí
        120, // Chi phí thuê group
        120, // Ngân sách
        120, // Chi phí thuê TK
        120, // Chi phí phòng ND
        100, // Tổng IB
        100, // Tổng SĐT
        100, // Tổng SĐT nội thành
        100, // Tổng SĐT tỉnh
        100, // Tỷ lệ SĐT tỉnh (%)
        100, // Tổng SĐT tiếp cận
        100, // Tỷ lệ tiếp cận (%)
        120, // Chi phí / IB
        120, // Chi phí / Số
        100, // Tỉ lệ xin số (%)
        100, // Lịch đặt trong ngày
        100, // Tỉ lệ đặt lịch trong ngày (%)
        100, // Lịch đặt trong tháng
        100, // Tỉ lệ đặt lịch trong tháng (%)
        120, // Chi phí / lịch đặt trong ngày
        120, // Chi phí / lịch đặt trong tháng
        100, // Khách mới
        100, // Khách vãng lai
        100, // Khách fail
        100, // Khách cọc
        100, // Khách fail + cọc
        100, // Khách CS từ chối điều trị
        100, // Tỉ lệ fail tại CS (%)
        100, // Tỉ lệ fail+cọc tại CS (%)
        100, // Tỉ lệ khách thực qua / SĐT (%)
        100, // Tỉ lệ đặt lịch trong ngày (%)
        130, // Tỉ lệ khách thực qua / Lịch chốt (%)
        120, // Chi phí MKT cho mỗi KH đến CS
        200, // Chi phí MKT cho mỗi KH đến CS và thực hiện dịch vụ
        200, // Đầu hóa đơn mỗi KHM đến CS và thực hiện dịch vụ
        100, // Đầu hóa đơn khách mới
        100, // Doanh thu khách cũ (%)
        100, // Hiệu quả
        100, // Phục vụ
    ];

    public static function getFormulas(array $departmentIds = []): array
    {
        $formulas = self::STATIC_FORMULAS;
        $formulas['ACCOUNT_FEE'] = self::getAccountFeeFormula($departmentIds);
        return array_values($formulas);
    }

    public static function getAccountFeeFormula(array $departmentIds = []): string
    {
        $isDept6Selected = in_array('6', $departmentIds) || in_array(6, $departmentIds);
        
        if ($isDept6Selected && count($departmentIds) == 1) {
            $newRatio = config('common.account_fee_ratio_dept6_new') * 100;
            $oldRatio = config('common.account_fee_ratio_dept6_old') * 100;
            return "10=9*{$newRatio}%\n(Trước tháng 05/2025: {$oldRatio}%)";
        } else {
            $newRatio = config('common.account_fee_ratio_others_new') * 100;
            $oldRatio = config('common.account_fee_ratio_others_old') * 100;
            return "10=9*{$newRatio}%\n(Trước tháng 01/2025: {$oldRatio}%)";
        }
    }

    public static function getHeaderStyles(): array
    {
        return array_values(self::HEADER_STYLES);
    }
}
