<?php

namespace App\Constants\LeadReport;

class LeadReportTableConstants
{
    public const HEADERS = [
        "STT",
        "Ngà<PERSON> nhập",
        "<PERSON><PERSON><PERSON> thu",
        "Tổng chi phí / <PERSON><PERSON><PERSON> thu (%)",
        "Tỉ lệ khách thực qua / SĐT (%)",
        "Tỉ lệ đặt lịch trong ngày (%)",
        "Tỉ lệ đặt lịch trong tháng (%)",
        "Doanh thu KH mới",
        "Doanh thu KH cũ",
        "Doanh thu KH khác",
        "<PERSON><PERSON>a đơn / KH",
        "<PERSON><PERSON>a đơn/ khách mới",
        "Do<PERSON><PERSON> thu / SĐT",
        "Doanh thu KH mới / SĐT",
        "Tổng chi phí",
        "Chi phí thuê group",
        "Ngân sách",
        "Chi phí thuê TK",
        "Chi phí phòng ND",
        "Tổng IB",
        "Tổng SĐT",
        "Tổng SĐT nội thành",
        "Tổng SĐT tỉnh",
        "Tỷ lệ SĐT tỉnh (%)",
        "Tổng SĐT tiếp cận",
        "Tỷ lệ tiếp cận (%)",
        "Chi phí / IB",
        "Chi phí / Số",
        "Tỉ lệ xin số (%)",
        "Lịch đặt trong ngày",
        "Tỉ lệ đặt lịch trong ngày (%)",
        "Lịch đặt trong tháng",
        "Tỉ lệ đặt lịch trong tháng (%)",
        "Chi phí / lịch đặt trong ngày",
        "Chi phí / lịch đặt trong tháng",
        "Khách mới",
        "Khách vãng lai",
        "Khách fail",
        "Khách cọc",
        "Khách fail + cọc",
        "Khách CS từ chối điều trị",
        "Tỉ lệ fail tại CS (%)",
        "Tỉ lệ fail+cọc tại CS (%)",
        "Tỉ lệ khách thực qua / SĐT (%)",
        "Tỉ lệ đặt lịch trong ngày (%)",
        "Tỉ lệ khách thực qua / Lịch chốt (%)",
        "Chi phí MKT cho mỗi KH đến CS",
        "Chi phí MKT cho mỗi KH đến CS và thực hiện dịch vụ",
        "Đầu hóa đơn mỗi KHM đến CS và thực hiện dịch vụ",
        "Đầu hóa đơn khách mới",
        "Doanh thu khách cũ (%)",
        "Hiệu quả dịch vụ của bác sĩ",
        "Thái độ phục vụ của EKIP",
    ];

    public const FORMULAS = [
        "",  // STT
        "",  // Ngày nhập
        "1=3+4+5",
        "2=7/1",
        "32=24/13",
        "20=19/13",
        "22=21/13",
        "3",
        "4",
        "5",
        "6=1/24",
        "6.0=3/24",
        "6.1=1/13",
        "6.2=3/13",
        "7=8+9+10+11\n(TH Seeding: 7=(8+9+10+11)*1.5)",
        "8",
        "9",
        "10 = 9*3%\n(Trước tháng 01/2025 là 2%)", // Will be replaced dynamically
        "11",
        "12",
        "13=13.1+13.2",
        "13.1",
        "13.2",
        "13.3=13.2/13",
        "14",
        "15=14/13",
        "16=7/12",
        "17=7/13",
        "18=13/12",
        "19",
        "20=19/13",
        "21",
        "22=21/13",
        "23=7/19",
        "23.1=7/21",
        "24",
        "25",
        "26",
        "27",
        "28=26+27",
        "29",
        "30=26/24",
        "31=(26+27)/24",
        "32=24/13",
        "20=19/13",
        "33=24/18",
        "34=7/24",
        "35=7/(24-26)",
        "36=3/(24-26)",
        "37=3/24",
        "38=4/1",
        "39", // Hiệu quả dịch vụ của bác sĩ
        "40", // Thái độ phục vụ của EKIP
    ];

    public const WIDTH_OF_COLUMNS = [
        60, // STT
        100, // Ngày nhập
        120, // Doanh thu
        100, // Tổng chi phí / Doanh thu (%)
        120, // Tỉ lệ khách thực qua / SĐT (%)
        100, // Tỉ lệ đặt lịch trong ngày (%)
        100, // Tỉ lệ đặt lịch trong tháng (%)
        120, // Doanh thu KH mới
        120, // Doanh thu KH cũ
        120, // Doanh thu KH khác
        120, // Hóa đơn / KH
        120, // Hóa đơn/ khách mới
        120, // Doanh thu / SĐT
        120, // Doanh thu KH mới / SĐT
        120, // Tổng chi phí
        120, // Chi phí thuê group
        120, // Ngân sách
        120, // Chi phí thuê TK
        120, // Chi phí phòng ND
        100, // Tổng IB
        100, // Tổng SĐT
        100, // Tổng SĐT nội thành
        100, // Tổng SĐT tỉnh
        100, // Tỷ lệ SĐT tỉnh (%)
        100, // Tổng SĐT tiếp cận
        100, // Tỷ lệ tiếp cận (%)
        120, // Chi phí / IB
        120, // Chi phí / Số
        100, // Tỉ lệ xin số (%)
        100, // Lịch đặt trong ngày
        100, // Tỉ lệ đặt lịch trong ngày (%)
        100, // Lịch đặt trong tháng
        100, // Tỉ lệ đặt lịch trong tháng (%)
        120, // Chi phí / lịch đặt trong ngày
        120, // Chi phí / lịch đặt trong tháng
        100, // Khách mới
        100, // Khách vãng lai
        100, // Khách fail
        100, // Khách cọc
        100, // Khách fail + cọc
        100, // Khách CS từ chối điều trị
        100, // Tỉ lệ fail tại CS (%)
        100, // Tỉ lệ fail+cọc tại CS (%)
        100, // Tỉ lệ khách thực qua / SĐT (%)
        100, // Tỉ lệ đặt lịch trong ngày (%)
        130, // Tỉ lệ khách thực qua / Lịch chốt (%)
        120, // Chi phí MKT cho mỗi KH đến CS
        200, // Chi phí MKT cho mỗi KH đến CS và thực hiện dịch vụ
        200, // Đầu hóa đơn mỗi KHM đến CS và thực hiện dịch vụ
        100, // Đầu hóa đơn khách mới
        100, // Doanh thu khách cũ (%)
        100, // Hiệu quả
        100, // Phục vụ
    ];

    /**
     * Get formulas with dynamic account fee calculation for TikTok vs Traditional ADS
     */
    public static function getFormulas(array $departmentIds = []): array
    {
        $formulas = self::FORMULAS;
        // Update account fee formula dynamically based on department (TikTok vs Traditional)
        $formulas[17] = self::getAccountFeeFormula($departmentIds);
        return $formulas;
    }

    /**
     * Get account fee formula based on department selection (TikTok vs Traditional ADS)
     */
    public static function getAccountFeeFormula(array $departmentIds = []): string
    {
        $isDept6Selected = in_array('6', $departmentIds) || in_array(6, $departmentIds);

        if ($isDept6Selected && count($departmentIds) == 1) {
            // Only TikTok department (ID 6) is selected
            $newRatio = config('common.account_fee_ratio_dept6_new') * 100;
            $oldRatio = config('common.account_fee_ratio_dept6_old') * 100;
            return "10 = 9*{$newRatio}%\n(Trước tháng 05/2025 là {$oldRatio}%)";
        } else {
            // Traditional ADS departments or mixed selection
            $newRatio = config('common.account_fee_ratio_others_new') * 100;
            $oldRatio = config('common.account_fee_ratio_others_old') * 100;
            return "10 = 9*{$newRatio}%\n(Trước tháng 01/2025 là {$oldRatio}%)";
        }
    }

    /**
     * Get header styles optimized for API response with proper text display
     */
    public static function getHeaderStyles(): array
    {
        return [
            // STT
            ['width' => 60, 'minWidth' => 60, 'textAlign' => 'center', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Ngày nhập
            ['width' => 120, 'minWidth' => 120, 'textAlign' => 'center', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Doanh thu
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Tổng chi phí / Doanh thu (%)
            ['width' => 180, 'minWidth' => 180, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tỉ lệ khách thực qua / SĐT (%)
            ['width' => 200, 'minWidth' => 200, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tỉ lệ đặt lịch trong ngày (%)
            ['width' => 180, 'minWidth' => 200, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tỉ lệ đặt lịch trong tháng (%)
            ['width' => 180, 'minWidth' => 180, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Doanh thu KH mới
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Doanh thu KH cũ
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Doanh thu KH khác
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Hóa đơn / KH
            ['width' => 120, 'minWidth' => 120, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Hóa đơn/ khách mới
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Doanh thu / SĐT
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Doanh thu KH mới / SĐT
            ['width' => 160, 'minWidth' => 160, 'textAlign' => 'right', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tổng chi phí
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Chi phí thuê group
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Ngân sách
            ['width' => 120, 'minWidth' => 120, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Chi phí thuê TK (TikTok vs Traditional ADS)
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'right', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Chi phí phòng ND
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Tổng IB
            ['width' => 100, 'minWidth' => 100, 'textAlign' => 'center', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Tổng SĐT
            ['width' => 100, 'minWidth' => 100, 'textAlign' => 'center', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Tổng SĐT nội thành
            ['width' => 160, 'minWidth' => 160, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tổng SĐT tỉnh
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'center', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Tỷ lệ SĐT tỉnh (%)
            ['width' => 160, 'minWidth' => 160, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tổng SĐT tiếp cận
            ['width' => 160, 'minWidth' => 160, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tỷ lệ tiếp cận (%)
            ['width' => 160, 'minWidth' => 160, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Chi phí / IB
            ['width' => 120, 'minWidth' => 120, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Chi phí / Số
            ['width' => 120, 'minWidth' => 120, 'textAlign' => 'right', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Tỉ lệ xin số (%)
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'center', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Lịch đặt trong ngày
            ['width' => 160, 'minWidth' => 160, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tỉ lệ đặt lịch trong ngày (%)
            ['width' => 180, 'minWidth' => 180, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Lịch đặt trong tháng
            ['width' => 160, 'minWidth' => 160, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tỉ lệ đặt lịch trong tháng (%)
            ['width' => 180, 'minWidth' => 180, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Chi phí / lịch đặt trong ngày
            ['width' => 200, 'minWidth' => 200, 'textAlign' => 'right', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Chi phí / lịch đặt trong tháng
            ['width' => 200, 'minWidth' => 200, 'textAlign' => 'right', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Khách mới
            ['width' => 100, 'minWidth' => 100, 'textAlign' => 'center', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Khách vãng lai
            ['width' => 120, 'minWidth' => 120, 'textAlign' => 'center', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Khách fail
            ['width' => 100, 'minWidth' => 100, 'textAlign' => 'center', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Khách cọc
            ['width' => 100, 'minWidth' => 100, 'textAlign' => 'center', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Khách fail + cọc
            ['width' => 140, 'minWidth' => 140, 'textAlign' => 'center', 'whiteSpace' => 'nowrap', 'fontWeight' => 'bold'],
            // Khách CS từ chối điều trị
            ['width' => 180, 'minWidth' => 180, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tỉ lệ fail tại CS (%)
            ['width' => 160, 'minWidth' => 160, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tỉ lệ fail+cọc tại CS (%)
            ['width' => 180, 'minWidth' => 180, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tỉ lệ khách thực qua / SĐT (%) - duplicate
            ['width' => 200, 'minWidth' => 200, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tỉ lệ đặt lịch trong ngày (%) - duplicate
            ['width' => 180, 'minWidth' => 180, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Tỉ lệ khách thực qua / Lịch chốt (%)
            ['width' => 220, 'minWidth' => 220, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Chi phí MKT cho mỗi KH đến CS
            ['width' => 200, 'minWidth' => 200, 'textAlign' => 'right', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Chi phí MKT cho mỗi KH đến CS và thực hiện dịch vụ
            ['width' => 280, 'minWidth' => 280, 'textAlign' => 'right', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Đầu hóa đơn mỗi KHM đến CS và thực hiện dịch vụ
            ['width' => 280, 'minWidth' => 280, 'textAlign' => 'right', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Đầu hóa đơn khách mới
            ['width' => 160, 'minWidth' => 160, 'textAlign' => 'right', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Doanh thu khách cũ (%)
            ['width' => 160, 'minWidth' => 160, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Hiệu quả dịch vụ của bác sĩ
            ['width' => 180, 'minWidth' => 180, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
            // Thái độ phục vụ của EKIP
            ['width' => 180, 'minWidth' => 180, 'textAlign' => 'center', 'whiteSpace' => 'normal', 'fontWeight' => 'bold', 'wordWrap' => 'break-word'],
        ];
    }
}
