<?php

namespace App\Constants\LeadReport;

class LeadReportTableConstants
{
    public const HEADERS = [
        "STT",
        "Ngà<PERSON> nhập",
        "<PERSON><PERSON><PERSON> thu",
        "Tổng chi phí / <PERSON><PERSON><PERSON> thu (%)",
        "Tỉ lệ khách thực qua / SĐT (%)",
        "Tỉ lệ đặt lịch trong ngày (%)",
        "Tỉ lệ đặt lịch trong tháng (%)",
        "Doanh thu KH mới",
        "Doanh thu KH cũ",
        "Doanh thu KH khác",
        "<PERSON><PERSON>a đơn / KH",
        "<PERSON><PERSON>a đơn/ khách mới",
        "Do<PERSON><PERSON> thu / SĐT",
        "Doanh thu KH mới / SĐT",
        "Tổng chi phí",
        "Chi phí thuê group",
        "Ngân sách",
        "Chi phí thuê TK",
        "Chi phí phòng ND",
        "Tổng IB",
        "Tổng SĐT",
        "Tổng SĐT nội thành",
        "Tổng SĐT tỉnh",
        "Tỷ lệ SĐT tỉnh (%)",
        "Tổng SĐT tiếp cận",
        "Tỷ lệ tiếp cận (%)",
        "Chi phí / IB",
        "Chi phí / Số",
        "Tỉ lệ xin số (%)",
        "Lịch đặt trong ngày",
        "Tỉ lệ đặt lịch trong ngày (%)",
        "Lịch đặt trong tháng",
        "Tỉ lệ đặt lịch trong tháng (%)",
        "Chi phí / lịch đặt trong ngày",
        "Chi phí / lịch đặt trong tháng",
        "Khách mới",
        "Khách vãng lai",
        "Khách fail",
        "Khách cọc",
        "Khách fail + cọc",
        "Khách CS từ chối điều trị",
        "Tỉ lệ fail tại CS (%)",
        "Tỉ lệ fail+cọc tại CS (%)",
        "Tỉ lệ khách thực qua / SĐT (%)",
        "Tỉ lệ đặt lịch trong ngày (%)",
        "Tỉ lệ khách thực qua / Lịch chốt (%)",
        "Chi phí MKT cho mỗi KH đến CS",
        "Chi phí MKT cho mỗi KH đến CS và thực hiện dịch vụ",
        "Đầu hóa đơn mỗi KHM đến CS và thực hiện dịch vụ",
        "Đầu hóa đơn khách mới",
        "Doanh thu khách cũ (%)",
        "Hiệu quả dịch vụ của bác sĩ",
        "Thái độ phục vụ của EKIP",
    ];

    public const FORMULAS = [
        "",  // STT
        "",  // Ngày nhập
        "1=3+4+5",
        "2=7/1",
        "32=24/13",
        "20=19/13",
        "22=21/13",
        "3",
        "4",
        "5",
        "6=1/24",
        "6.0=3/24",
        "6.1=1/13",
        "6.2=3/13",
        "7=8+9+10+11\n(TH Seeding: 7=(8+9+10+11)*1.5)",
        "8",
        "9",
        "10 = 9*3%\n(Trước tháng 01/2025 là 2%)", // Will be replaced dynamically
        "11",
        "12",
        "13=13.1+13.2",
        "13.1",
        "13.2",
        "13.3=13.2/13",
        "14",
        "15=14/13",
        "16=7/12",
        "17=7/13",
        "18=13/12",
        "19",
        "20=19/13",
        "21",
        "22=21/13",
        "23=7/19",
        "23.1=7/21",
        "24",
        "25",
        "26",
        "27",
        "28=26+27",
        "29",
        "30=26/24",
        "31=(26+27)/24",
        "32=24/13",
        "20=19/13",
        "33=24/18",
        "34=7/24",
        "35=7/(24-26)",
        "36=3/(24-26)",
        "37=3/24",
        "38=4/1",
        "39", // Hiệu quả dịch vụ của bác sĩ
        "40", // Thái độ phục vụ của EKIP
    ];

    public const WIDTH_OF_COLUMNS = [
        60,   // STT
        120,  // Ngày nhập (120px phù hợp cho định dạng ngày)
        140,  // Doanh thu (giá trị lớn)
        180,  // Tổng chi phí / Doanh thu (%) – cần rộng để hiển thị công thức %
        200,  // Tỉ lệ khách thực qua / SĐT (%) – text dài
        180,  // Tỉ lệ đặt lịch trong ngày (%) – text dài và dấu %
        180,  // Tỉ lệ đặt lịch trong tháng (%) – tương tự trên
        150,  // Doanh thu KH mới
        150,  // Doanh thu KH cũ
        150,  // Doanh thu KH khác
        140,  // Hóa đơn / KH
        160,  // Hóa đơn/ khách mới
        140,  // Doanh thu / SĐT
        170,  // Doanh thu KH mới / SĐT
        160,  // Tổng chi phí
        160,  // Chi phí thuê group
        140,  // Ngân sách
        140,  // Chi phí thuê TK
        140,  // Chi phí phòng ND
        120,  // Tổng IB
        120,  // Tổng SĐT
        140,  // Tổng SĐT nội thành
        120,  // Tổng SĐT tỉnh
        160,  // Tỷ lệ SĐT tỉnh (%) – thêm khoảng cho %
        140,  // Tổng SĐT tiếp cận
        140,  // Tỉ lệ tiếp cận (%) – thêm khoảng
        140,  // Chi phí / IB
        140,  // Chi phí / Số
        140,  // Tỉ lệ xin số (%) – text có dấu %
        180,  // Lịch đặt trong ngày
        200,  // Tỉ lệ đặt lịch trong ngày (%) – text rất dài
        180,  // Lịch đặt trong tháng
        200,  // Tỉ lệ đặt lịch trong tháng (%) – text rất dài
        200,  // Chi phí / lịch đặt trong ngày
        200,  // Chi phí / lịch đặt trong tháng
        120,  // Khách mới
        120,  // Khách vãng lai
        120,  // Khách fail
        120,  // Khách cọc
        120,  // Khách fail + cọc
        120,  // Khách CS từ chối điều trị
        160,  // Tỉ lệ fail tại CS (%) – thêm khoảng
        180,  // Tỉ lệ fail+cọc tại CS (%) – text dài hơn
        200,  // Tỉ lệ khách thực qua / SĐT (%) – phục vụ cho văn bản dài
        200,  // Tỉ lệ đặt lịch trong ngày (%) – ...
        240,  // Tỉ lệ khách thực qua / Lịch chốt (%) – text dài
        180,  // Chi phí MKT cho mỗi KH đến CS – text dài cần đủ rộng
        240,  // Chi phí MKT cho mỗi KH đến CS và thực hiện dịch vụ – rất dài
        220,  // Đầu hóa đơn mỗi KHM đến CS và thực hiện dịch vụ – rất dài
        160,  // Đầu hóa đơn khách mới
        120,  // Doanh thu khách cũ (%)
        140,  // Hiệu quả dịch vụ của bác sĩ – thêm khoảng để nhìn rõ hơn
        160,  // Thái độ phục vụ của EKIP – chữ dài nên tăng
    ];

    /**
     * Get formulas with dynamic account fee calculation for TikTok vs Traditional ADS
     */
    public static function getFormulas(array $departmentIds = []): array
    {
        $formulas = self::FORMULAS;
        // Update account fee formula dynamically based on department (TikTok vs Traditional)
        $formulas[17] = self::getAccountFeeFormula($departmentIds);
        return $formulas;
    }

    /**
     * Get account fee formula based on department selection (TikTok vs Traditional ADS)
     */
    public static function getAccountFeeFormula(array $departmentIds = []): string
    {
        $isDept6Selected = in_array('6', $departmentIds) || in_array(6, $departmentIds);

        if ($isDept6Selected && count($departmentIds) == 1) {
            // Only TikTok department (ID 6) is selected
            $newRatio = config('common.account_fee_ratio_dept6_new') * 100;
            $oldRatio = config('common.account_fee_ratio_dept6_old') * 100;
            return "10 = 9*{$newRatio}%\n(Trước tháng 05/2025 là {$oldRatio}%)";
        } else {
            // Traditional ADS departments or mixed selection
            $newRatio = config('common.account_fee_ratio_others_new') * 100;
            $oldRatio = config('common.account_fee_ratio_others_old') * 100;
            return "10 = 9*{$newRatio}%\n(Trước tháng 01/2025 là {$oldRatio}%)";
        }
    }


}
