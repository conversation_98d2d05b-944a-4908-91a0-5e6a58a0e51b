<?php

namespace App\Services\LeadReportStatistic;

use App\Contracts\Repositories\BusinessDepartmentRepositoryInterface;
use App\Contracts\Repositories\DepartmentRepositoryInterface;
use App\Contracts\Repositories\DoctorRepositoryInterface;
use App\Contracts\Repositories\FastFilterLeadReportRepositoryInterface;
use App\Contracts\Repositories\MarketingTeamFilterRepositoryInterface;
use App\Contracts\Repositories\MarketingTeamRepositoryInterface;
use App\Contracts\Repositories\ProductRepositoryInterface;
use App\Contracts\Services\LeadReport\LeadReportStatisticTypeInterface;
use App\Enums\LeadReport\AreaEnum;
use App\Enums\LeadReport\ReportSearchTypeEnum;
use App\Exports\LeadReport\LeadReportExport;
use App\Models\LeadReport;
use App\Models\User;
use App\Services\BaseService;
use App\Services\MarketingTeam\MarketingTeamService;
use App\Services\Traits\HasPrevPeriod;
use App\Services\Traits\ManagesDoctorToLeadReportForUser;
use App\Services\Traits\ManagesShopForUser;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Constants\LeadReport\LeadReportTableConstants;

class LeadReportStatisticService extends BaseService
{
    use HasPrevPeriod;
    use ManagesShopForUser;
    use ManagesDoctorToLeadReportForUser;

    protected ?LeadReportStatisticTypeInterface $leadReportStatisticType;

    protected DepartmentRepositoryInterface $departmentRepository;
    protected ProductRepositoryInterface $productRepository;
    protected DoctorRepositoryInterface $doctorRepository;
    protected FastFilterLeadReportRepositoryInterface $fastFilterLeadReport;
    protected MarketingTeamService $marketingTeamService;
    protected BusinessDepartmentRepositoryInterface $businessDepartmentRepository;


    public function __construct(
        DepartmentRepositoryInterface           $departmentRepository,
        ProductRepositoryInterface              $productRepository,
        DoctorRepositoryInterface               $doctorRepository,
        FastFilterLeadReportRepositoryInterface $fastFilterLeadReport,
        MarketingTeamService                    $marketingTeamService,
        BusinessDepartmentRepositoryInterface   $businessDepartmentRepository
    ) {
        $this->departmentRepository = $departmentRepository;
        $this->productRepository = $productRepository;
        $this->doctorRepository = $doctorRepository;
        $this->fastFilterLeadReport = $fastFilterLeadReport;
        $this->marketingTeamService = $marketingTeamService;
        $this->businessDepartmentRepository = $businessDepartmentRepository;
    }

    public function setLeadReportStatisticType(?LeadReportStatisticTypeInterface $leadReportStatisticType): static
    {
        $this->leadReportStatisticType = $leadReportStatisticType;

        return $this;
    }

    public function getReportsData(array $filters, User $viewer): array
    {
        $data['filters'] = $this->getFiltersData(request(), $viewer);
        $data['fastFilters'] = $this->fastFilterLeadReport->getByUserIds($viewer->id);

        // Replace filter current date
        $data['fastFilters'] = $this->replaceFilterTimePeriod($data['fastFilters']);

        if (!$this->leadReportStatisticType) {
            return $data;
        }

        $data['reportsData'] = $this->leadReportStatisticType->getReportsData($viewer, $filters);
        $data['reportsTableDataHtml'] = $this->leadReportStatisticType->getReportsTableView($data)->render();

        return $data;
    }

    private function replaceFilterTimePeriod($fastFilters)
    {
        $currentDate = Carbon::now();

        foreach ($fastFilters as $fastFilter) {
            $originalUrl = $fastFilter->url;

            // Set(replace) start_date_at to the first day of the current month
            $startOfMonth = $currentDate->copy()->startOfMonth()
                ->format(config('common.datetime.format.client.date'));
            $originalUrl = preg_replace(
                '/start_date_at=[^&]+/',
                'start_date_at=' . urlencode($startOfMonth),
                $originalUrl
            );

            // Set(replace) end_date_at to the current day - 1 (keep current day if it's the first day)
            $endOfDay = $currentDate->copy()->format(config('common.datetime.format.client.date'));

            if ($endOfDay != $startOfMonth) {
                $endOfDay = Carbon::createFromFormat(config('common.datetime.format.client.date'), $endOfDay)
                    ->subDay()->format(config('common.datetime.format.client.date'));
            }

            $originalUrl = preg_replace(
                '/end_date_at=[^&]+/',
                'end_date_at=' . urlencode($endOfDay),
                $originalUrl
            );

            $fastFilter->url = $originalUrl;
        }

        return $fastFilters;
    }

    protected function getFiltersData(Request $request, User $user): array
    {
        return [
            'data' => [
                'shops' => $this->getOptionsShopsWithRegionsAndTotal($user, LeadReport::class),
                'departments' => $this->marketingTeamService->getFiltersDepartments($user, $request->query('department_type')),
                'products' => $this->productRepository->allMainProducts(),
                'products_meso' => $this->productRepository->allMainProductsMeso(),
                'reportTypes' => $this->getFiltersReportTypes($user),
                'areas' => $this->getFiltersAreasData(),
                'doctors' => $this->getAvailableDoctorsToLeadReportByUser($user),
                'prevPeriodMonths' => get_prev_period_months(),
                'marketingTeamsTypeDepartment' => $this->getMarketingTeamsTypeDepartment($user),
                'businessDepartments' => $this->businessDepartmentRepository->all(),
                'availableMonths' => $this->getAvailableMonths(now()->year),
                'availableYears' => $this->getAvailableYears(),
                'marketingTeamFilters' => app(MarketingTeamFilterRepositoryInterface::class)->pluck('name', 'id')->toArray(),
            ],
            'params' => [
                'report_type' => $request->query('report_type'),
                'shop_id' => $request->query('shop_id'),
                'department_ids' => $request->query('department_ids'),
                'department_id' => $request->query('department_id'),
                'marketing_team_ids' => $request->query('marketing_team_ids'),
                'marketing_team_type_doctor_ids' => $request->query('marketing_team_type_doctor_ids'),
                'product_id' => $request->query('product_id'),
                'product_ids' => $request->query('product_ids'),
                'doctor_ids' => $request->query('doctor_ids'),
                'area' => $request->query('area'),
                'prev_period_month' => $request->query('prev_period_month'),
                'start_date_at' => $request->query(
                    'start_date_at',
                    now()->subDay()->startOfMonth()->format(config('common.datetime.format.client.date'))
                ),
                'end_date_at' => $request->query(
                    'end_date_at',
                    now()->subDay()->format(config('common.datetime.format.client.date'))
                ),
                'doctor_id' => $request->query('doctor_id'),
                'show_avg_day_by_day' => (bool)$request->query('show_avg_day_by_day', true),
                'show_avg_last_3_months' => (bool)$request->query('show_avg_last_3_months'),
            ],
        ];
    }

    protected function getMarketingTeamsTypeDepartment(User $user)
    {
        $departments = $this->departmentRepository->with([
            'marketingTeams',
        ])->get();

        if ($user->isMarketingLeader()) {
            $marketingTeams = $user->marketingTeams->filter(function ($item) use ($departments) {
                return in_array($item->department_id, $departments->pluck('id')->toArray());
            });
        } else {
            $marketingTeams = $departments->pluck('marketingTeams')->flatten();
        }

        return $marketingTeams;
    }

    protected function getFiltersAreasData(): array
    {
        $areas = AreaEnum::getDescriptions();
        $areas[''] = 'TỔNG HỢP KHU VỰC';

        return $areas;
    }

    protected function getFiltersReportTypes(User $user): array
    {
        return ReportSearchTypeEnum::getDescriptions(
            ReportSearchTypeEnum::getAllowTypesByUser($user)
        );
    }

    public function exportExcel(array $filters)
    {
        $data = $this->getReportsData($filters, staff());
        $data['isExport'] = true;
        $nowFormat = now()->format(config('common.datetime.format.client.date_download'));
        $excelName = config('export.lead_report_search.excel_name');
        $extension = config('export.lead_report_search.extension_detector');
        $fileName = "{$nowFormat}_{$excelName}.{$extension}";

        return Excel::download(new LeadReportExport($data), $fileName);
    }

    public function getFiltersDataForApi(User $user): array
    {
        $departmentType = request()->query('department_type');
        $departments = $this->marketingTeamService->getFiltersDepartments($user, $departmentType);
        $marketingTeams = $this->marketingTeamService->getFiltersMarketingTeams($user, $departments->pluck('id')->toArray());
        array_unshift($marketingTeams, [
            'id' => '',
            'name' => 'Tất cả',
            'department_id' => '',
        ]);

        return [
            'report_types' => $this->getFiltersReportTypes($user),
            'shops' => $this->getOptionsShopsWithRegionsAndTotal($user, LeadReport::class),
            'departments' => array_values($departments->map->only(['id', 'name'])->toArray()),
            'products' => $this->productRepository->allMainProducts()->map->only(['id', 'name'])->toArray(),
            'meso_product_ids' => $this->productRepository->allMainProductsMeso(),
            'areas' => $this->getFiltersAreasData(),
            'marketing_teams' => $marketingTeams,
            'doctors' => $this->getAvailableDoctorsToLeadReportByUser($user)->map->only(['id', 'name']),
            'prev_period_months' => get_prev_period_months(),
        ];
    }

    public function getFiltersDoctors($conditions = [])
    {
        if (!isset($conditions['start_date_at']) || !isset($conditions['end_date_at'])) {
            return [];
        }

        $conditions['start_date_at'] = Carbon::createFromFormat(config('common.datetime.format.client.date'), $conditions['start_date_at'])->format(config('common.datetime.format.database.date'));
        $conditions['end_date_at'] = Carbon::createFromFormat(config('common.datetime.format.client.date'), $conditions['end_date_at'])->format(config('common.datetime.format.database.date'));

        return $this->doctorRepository->getDoctorsByMktTeamIds($conditions)->toArray();
    }

    public function getMarketingTeamsByBusinessDepartment($businessDepartmentId): array
    {
        $marketingTeams = app(MarketingTeamRepositoryInterface::class)
            ->getByBusinessDepartmentId($businessDepartmentId);

        return $marketingTeams->pluck('id')->toArray();
    }

    public function leadReportGetFiltersMarketingTeams(User $user, array $conditions = []): array
    {
        // if (!isset($conditions['start_date_at']) || !isset($conditions['end_date_at'])) {
        //     return [];
        // }

        // $conditions['start_date_at'] = Carbon::createFromFormat(config('common.datetime.format.client.date'), $conditions['start_date_at'])->format(config('common.datetime.format.database.date'));
        // $conditions['end_date_at'] = Carbon::createFromFormat(config('common.datetime.format.client.date'), $conditions['end_date_at'])->format(config('common.datetime.format.database.date'));

        $departments = $this->departmentRepository->with([
            'marketingTeams' => function ($query) use ($conditions) {
                // Filter theo team_type nếu có và không phải 'all'
                if (isset($conditions['team_type']) && $conditions['team_type'] !== 'all') {
                    $query->where('team_type', $conditions['team_type']);
                }

                // $query->whereHas('leadReports', function ($query) use ($conditions) {
                    // $query->whereBetween('report_date_at', [$conditions['start_date_at'], $conditions['end_date_at']]);
                // });
            }
        ])->getByIds($conditions['department_ids']);

        if ($user->isMarketingLeader()) {
            $marketingTeams = $user->marketingTeams->filter(function ($item) use ($departments, $conditions) {
                $isInDepartment = in_array($item->department_id, $departments->pluck('id')->toArray());

                // Thêm filter theo team_type nếu có và không phải 'all'
                $matchesTeamType = !isset($conditions['team_type']) || $conditions['team_type'] === 'all' || $item->team_type == $conditions['team_type'];

                return $isInDepartment && $matchesTeamType;
            });
        } else {
            $marketingTeams = $departments->pluck('marketingTeams')->flatten();
        }

        $marketingTeams = $this->filterMarketingTeamsByPermission($marketingTeams);

        return $marketingTeams->map(function ($team) {
            return [
                'id' => $team->id,
                'name' => $team->name,
                'department_id' => $team->department_id,
                'business_department_id' => $team->business_department_id,
                'team_type' => $team->team_type,
            ];
        })->toArray();
    }

    protected function filterMarketingTeamsByPermission($marketingTeams)
    {
        if (!staff()->hasAdministratorRoles() && staff()->can('limitByMktTeam', LeadReport::class)) {
            $mktTeamIds = staff()->marketingTeams->pluck('id')->toArray();
            return $marketingTeams->whereIn('id', $mktTeamIds);
        }

        return $marketingTeams;
    }

    protected function getAvailableYears(): array
    {
        /*if (staff()->can('viewReportLast2Months', LeadReport::class)) {
            $carbonYears = [];
            $now = Carbon::now();

            foreach (range(0, 2) as $i) {
                if (!in_array($carbonYear = $now->copy()->subMonths($i)->year, $carbonYears)) {
                    $carbonYears[$carbonYear] = $carbonYear;
                }
            }

            return $carbonYears;
        }*/

        return collect(range(2023, now()->year))
            ->reverse()
            ->reduce(function ($rangeYears, $year) {
                $rangeYears[$year] = $year;
                return $rangeYears;
            }) ?? [];
    }

    protected function getAvailableMonths(int $year): array
    {
        /*if (staff()->can('viewReportLast2Months', LeadReport::class)) {
            $carbonMonths = [];
            $now = Carbon::now();

            foreach (range(0, 2) as $i) {
                $carbonMonth = $now->copy()->subMonths($i);

                if ($carbonMonth->year === $year) {
                    $carbonMonths[] = $carbonMonth;
                }
            }

            return array_merge($months, collect($carbonMonths)->reverse()->reduce(function ($rangeMonths, $carbonMonth) {
                $rangeMonths[$carbonMonth->month] = 'Tháng ' . $carbonMonth->month;
                return $rangeMonths;
            }) ?? []);
        }*/

        return [
            '0' => 'Chọn tháng',
            '13' => 'Hôm qua',
            '14' => 'Hôm nay',
            '1' => 'Tháng 1',
            '2' => 'Tháng 2',
            '3' => 'Tháng 3',
            '4' => 'Tháng 4',
            '5' => 'Tháng 5',
            '6' => 'Tháng 6',
            '7' => 'Tháng 7',
            '8' => 'Tháng 8',
            '9' => 'Tháng 9',
            '10' => 'Tháng 10',
            '11' => 'Tháng 11',
            '12' => 'Tháng 12',
        ];
    }

    public function getReportsApiData(array $filters, User $viewer): array
    {
        $isNotShowKpi = $this->leadReportStatisticType instanceof SingleDoctorLeadReportStatisticType || $this->leadReportStatisticType instanceof MultipleDoctorsLeadReportStatisticType || $this->leadReportStatisticType instanceof MultipleDepartmentLeadReportStatisticType;
        $isNotShowTotalBufferSeedingFee = $this->leadReportStatisticType instanceof SingleDoctorLeadReportStatisticType || $this->leadReportStatisticType instanceof MultipleDoctorsLeadReportStatisticType;
        $data = $this->getReportsData($filters, $viewer);
        $data['fastFilters'] = $this->fastFilterLeadReport->getByUserIds($viewer->id);
        $data['showKpi'] = !$isNotShowKpi;
        $data['showTotalBufferSeedingFee'] = !$isNotShowTotalBufferSeedingFee;

        // Replace filter current date
        $data['fastFilters'] = $this->replaceFilterTimePeriod($data['fastFilters']);

        if (!$this->leadReportStatisticType) {
            return $data;
        }

        $data['reportsData'] = $this->leadReportStatisticType->getReportsData($viewer, $filters);

        $response = [
            'headers' => LeadReportTableConstants::HEADERS,
            'formulas' => LeadReportTableConstants::getFormulas($filters['department_ids'] ?? []),
            'widthOfColumns' => LeadReportTableConstants::WIDTH_OF_COLUMNS,
            'headerStyles' => LeadReportTableConstants::getHeaderStyles(),
            'totals' => $this->transformDataToArray("TỔNG", "", $data['reportsData']['reports_total']),
            'dayAvg' => $this->transformDataToArray("TB Ngày", "", $data['reportsData']['reports_avg']),
            'last3MonthsAvg' => $this->transformDataToArray("TB 3T", "", $data['reportsData']['reports_avg_last_3_months']),
            'rows' => []
        ];

        if (isset($data['reportsData']['reports'])) {
            $index = 1;
            foreach ($data['reportsData']['reports'] as $date => $report) {
                $response['rows'][] = $this->transformDataToArray($index++, $date, $report, [
                    'showKpi' => $data['showKpi'],
                    'showTotalBufferSeedingFee' => $data['showTotalBufferSeedingFee'],
                ]);
            }
        }

        return $response;
    }

    private function transformDataToArray($label1, $label2, $data, $options = [
        'showKpi' => false,
        'showTotalBufferSeedingFee' => false,
    ]): array
    {
        if (!$data) return [];

        return [
            $label1,
            $label2,
            // Doanh thu
            $this->getRevenueData($data, $options),
            // Tổng chi phí / Doanh thu (%)
            $this->getRowData($data, 'feeRevenueRatio', 'func', 'percent', null, $options),
            // Tỉ lệ khách thực qua / SĐT (%)
            $this->getRowData($data, 'newCustomerPhoneNumberRatio', 'func', 'percent', null, $options),
            // Tỉ lệ đặt lịch TN (%)
            $this->getRowData($data, 'dateBookingPhoneNumberRatio', 'func', 'percent', null, $options),
            // Tỉ lệ đặt lịch TT (%)
            $this->getRowData($data, 'monthBookingPhoneNumberRatio', 'func', 'percent', null, $options),
            // Doanh thu KH mới
            $this->getRowData($data, 'new_customer_revenue', 'attribute', 'currency', null, $options),
            // Doanh thu KH cũ
            $this->getRowData($data, 'old_customer_revenue', 'attribute', 'currency', null, $options),
            // Doanh thu KH khác
            $this->getRowData($data, 'other_customer_revenue', 'attribute', 'currency', null, $options),
            // Hóa đơn / KH
            $this->getRowData($data, 'revenueNewCustomerAmount', 'func', 'currency', null, $options),
            // Hóa đơn/ khách mới
            $this->getRowData($data, 'newCustomerAverageBillAmount', 'func', 'currency', null, $options),
            // Doanh thu / SĐT
            $this->getRowData($data, 'revenuePerPhoneNumber', 'func', 'currency', null, $options),
            // Doanh thu KH mới / SĐT
            $this->getRowData($data, 'revenueNewCustomerPerPhoneNumber', 'func', 'currency', null, $options),
            // Tổng chi phí
            $this->getFeeData($data, $options),
            // Chi phí thuê group
            $this->getRowData($data, 'group_fee', 'attribute', 'currency', null, $options),
            // Ngân sách
            $this->getRowData($data, 'budget', 'attribute', 'currency', null, $options),
            // Chi phí thuê TK
            $this->getRowData($data, 'accountFee', 'func', 'currency', null, $options),
            // Chi phí phòng ND
            $this->getRowData($data, 'content_department_fee', 'attribute', 'currency', null, $options),
            // Tổng IB
            $this->getRowData($data, 'inbox_count', 'attribute', 'number', null, $options),
            // Tổng SĐT
            $this->getRowData($data, 'getPhoneNumberCount', 'func', 'number', null, $options),
            // Tổng SĐT nội thành
            $this->getRowData($data, 'phone_number_count', 'attribute', 'number', null, $options),
            // Tổng SĐT tỉnh
            $this->getRowData($data, 'province_phone_number_count', 'attribute', 'number', null, $options),
            // Tỷ lệ SĐT tỉnh (%)
            $this->getRowData($data, 'getProvincePhoneNumberRatio', 'func', 'percent', null, $options),
            // Tổng SĐT tiếp cận
            $this->getRowData($data, 'contacted_phone_number_count', 'attribute', 'number', null, $options),
            // Tỷ lệ tiếp cận (%)
            $this->getRowData($data, 'contactedPhoneNumberRatio', 'func', 'percent', null, $options),
            // Chi phí / IB
            $this->getRowData($data, 'feeInboxAmount', 'func', 'currency', null, $options),
            // Chi phí / Số
            $this->getRowData($data, 'feePhoneNumberAmount', 'func', 'currency', null, $options),
            // Tỉ lệ xin số (%)
            $this->getRowData($data, 'phoneNumberInboxRatio', 'func', 'percent', null, $options),
            // Lịch đặt TN
            $this->getRowData($data, 'date_booking_count', 'attribute', 'number', null, $options),
            // Tỉ lệ đặt lịch TN (%)
            $this->getRowData($data, 'dateBookingPhoneNumberRatio', 'func', 'percent', null, $options),
            // Lịch đặt TT
            $this->getRowData($data, 'month_booking_count', 'attribute', 'number', null, $options),
            // Tỉ lệ đặt lịch TT (%)
            $this->getRowData($data, 'monthBookingPhoneNumberRatio', 'func', 'percent', null, $options),
            // Chi phí / lịch đặt TN
            $this->getRowData($data, 'feeDateBookingAmount', 'func', 'currency', null, $options),
            // Chi phí / lịch đặt TT
            $this->getRowData($data, 'feeMonthBookingAmount', 'func', 'currency', null, $options),
            // Khách mới
            $this->getRowData($data, 'new_customer_count', 'attribute', 'number', null, $options),
            // Khách vãng lai
            $this->getRowData($data, 'random_customer_count', 'attribute', 'number', null, $options),
            // Khách fail
            $this->getRowData($data, 'failed_customer_count', 'attribute', 'number', null, $options),
            // Khách cọc
            $this->getRowData($data, 'paid_deposit_customer_count', 'attribute', 'number', null, $options),
            // Khách fail + cọc
            $this->getRowData($data, 'failedAndPaidDepositCustomerCountAmount', 'func', 'number', null, $options),
            // Khách CS từ chối điều trị
            $this->getRowData($data, 'customer_count_rejected_by_shop', 'attribute', 'number', null, $options),
            // Tỉ lệ fail tại CS (%)
            $this->getRowData($data, 'failedCustomerNewCustomerRatio', 'func', 'percent', null, $options),
            // Tỉ lệ fail+cọc tại CS (%)
            $this->getRowData($data, 'failedAndDepositCustomerNewCustomerRatio', 'func', 'percent', null, $options),
            // Tỉ lệ khách thực qua / SĐT (%)
            $this->getRowData($data, 'newCustomerPhoneNumberRatio', 'func', 'percent', null, $options),
            // Tỉ lệ đặt lịch TN (%)
            $this->getRowData($data, 'dateBookingPhoneNumberRatio', 'func', 'percent', null, $options),
            // Tỉ lệ khách thực qua / Lịch chốt (%)
            $this->getRowData($data, 'newCustomerMonthBookingRatio', 'func', 'percent', null, $options),
            // Chi phí MKT cho mỗi KH đến CS
            $this->getRowData($data, 'feeNewCustomerAmount', 'func', 'currency', null, $options),
            // Chi phí MKT cho mỗi KH đến CS và thực hiện dịch vụ
            $this->getRowData($data, 'feeServicedCustomerAmount', 'func', 'currency', null, $options),
            // Đầu hóa đơn mỗi KHM đến CS và thực hiện dịch vụ
            $this->getRowData($data, 'newCustomerRevenueServicedCustomerAmount', 'func', 'currency', null, $options),
            // Đầu hóa đơn khách mới
            $this->getRowData($data, 'newCustomerAverageBillAmount', 'func', 'currency', null, $options),
            // Doanh thu khách cũ (%)
            $this->getRowData($data, 'oldCustomerRevenueRatio', 'func', 'percent', null, $options),
            // Hiệu quả
            $this->getRatingsData($data, 'effective_point', 'doctor_rating', $options),
            // Phục vụ
            $this->getRatingsData($data, 'serve_point', 'shop_rating', $options),
        ];
    }

    private function getValue($object, $key, $type)
    {
        if ($type === 'func') {
            return $object->$key() ?? 0;
        }
        return $object->attributes()->$key ?? 0;
    }

    private function formatValue($value, $format)
    {
        if ($value === null) return null;

        return match($format) {
            'currency' => format_number_to_money_custom(reduce_money($value)),
            'percent' => format_number_to_percent_custom($value),
            'number' => $value,
            default => $value
        };
    }

    private function getRowData($data, $key, $type = 'func', $format = 'number', $optional = null, $options = []): array
    {
        $currentValue = $this->getValue($data['current'], $key, $type);
        $previousValue = isset($data['prev_range_time'])
            ? $this->getValue($data['prev_range_time'], $key, $type)
            : null;

        if ($optional) {
            $optionalValue = $this->getValue($data['current'], $optional, $type);
            $optionalPreviousValue = isset($data['prev_range_time'])
                ? $this->getValue($data['prev_range_time'], $optional, $type)
                : null;

            return [
                isset($optionalValue) ? sprintf("%s\n%s",
                    $this->formatValue($currentValue, $format),
                    $this->formatValue($optionalValue, $format)
                ) : $this->formatValue($currentValue, $format),
                isset($optionalPreviousValue) ? sprintf("%s\n%s",
                    $this->formatValue($previousValue, $format),
                    $this->formatValue($optionalPreviousValue, $format)
                ) : $this->formatValue($previousValue, $format)
            ];
        }

        return [
            $this->formatValue($currentValue, $format),
            $this->formatValue($previousValue, $format)
        ];
    }

    private function getRevenueData($data, $options = ['showKpi' => false]): array
    {
        return [
            format_number_to_money_custom(reduce_money($data['current']->revenue())),
            isset($data['prev_range_time']) ?
                sprintf(
                    "%s%s",
                    format_number_to_money_custom(reduce_money($data['prev_range_time']->revenue())),
                    !empty($options['showKpi']) ? " / " . format_number_to_money_custom(reduce_money($data['prev_shop_kpi']['revenue'] ?? 0)) : ""
                ) : null
        ];
    }

    private function getFeeData($data, $options = ['showTotalBufferSeedingFee' => false]): array
    {
        return [
            sprintf(
                "%s%s",
                format_number_to_money_custom(reduce_money($data['current']->fee())),
                !empty($options['showTotalBufferSeedingFee']) && $data['current']->totalBufferSeedingFee() > 100 ?
                    "\n(" . format_number_to_money_custom(reduce_money($data['current']->totalBufferSeedingFee())) . " của TH Seeding được x1.5)" : ""
            ),
            isset($data['prev_range_time']) ?
                sprintf(
                    "%s%s",
                    format_number_to_money_custom(reduce_money($data['prev_range_time']->fee())),
                    !empty($options['showTotalBufferSeedingFee']) && $data['prev_range_time']->totalBufferSeedingFee() > 100 ?
                        "\n(" . format_number_to_money_custom(reduce_money($data['prev_range_time']->totalBufferSeedingFee())) . " của TH Seeding được x1.5)" : ""
                ) : null
        ];
    }

    /**
     * Lấy dữ liệu ratings từ RatingService hoặc từ attributes của statistic
     *
     * @param array $data Dữ liệu báo cáo
     * @param string $attributeField Tên trường trong attributes (effective_point hoặc serve_point)
     * @param string $ratingField Tên trường trong ratings (doctor_rating hoặc shop_rating)
     * @param array $options Các tùy chọn
     * @return array Mảng chứa giá trị hiện tại và giá trị kỳ trước
     */
    private function getRatingsData($data, $attributeField, $ratingField, $options = []): array
    {
        $currentValue = $data['current']->attributes()[$attributeField] ?? 0;
        $previousValue = isset($data['prev_range_time']) ? ($data['prev_range_time']->attributes()[$attributeField] ?? 0) : null;

        // Lấy dữ liệu từ bảng ratings nếu có
        if (isset($data['current']->attributes()['report_date_at']) && !isset($data['is_total'])) {
            $date = $data['current']->attributes()['report_date_at'];
            $shopId = $data['current']->attributes()['shop_id'] ?? null;
            $doctorId = $data['current']->attributes()['doctor_id'] ?? null;

            if (app()->has('App\Services\RatingService')) {
                $ratingService = app('App\Services\RatingService');
                if ($doctorId) {
                    $ratings = $ratingService->getRatingsByDoctor($doctorId, $date, $date);
                } else {
                    $ratings = $ratingService->getRatingsByDate($date, $shopId, $doctorId);
                }

                if (!empty($ratings)) {
                    $currentValue = $ratings[$ratingField] ?? $currentValue;
                }
            }
        }

        // Tương tự cho kỳ trước nếu có
        if (isset($data['prev_range_time']) && isset($data['prev_range_time']->attributes()['report_date_at']) && !isset($data['is_total'])) {
            $prevDate = $data['prev_range_time']->attributes()['report_date_at'];
            $prevShopId = $data['prev_range_time']->attributes()['shop_id'] ?? null;
            $prevDoctorId = $data['prev_range_time']->attributes()['doctor_id'] ?? null;

            if (app()->has('App\Services\RatingService')) {
                $ratingService = app('App\Services\RatingService');
                if ($prevDoctorId) {
                    $prevRatings = $ratingService->getRatingsByDoctor($prevDoctorId, $prevDate, $prevDate);
                } else {
                    $prevRatings = $ratingService->getRatingsByDate($prevDate, $prevShopId, $prevDoctorId);
                }

                if (!empty($prevRatings)) {
                    $previousValue = $prevRatings[$ratingField] ?? $previousValue;
                }
            }
        }

        return [
            number_format($currentValue, 1) . '/10',
            $previousValue !== null ? number_format($previousValue, 1) . '/10' : null
        ];
    }
}
