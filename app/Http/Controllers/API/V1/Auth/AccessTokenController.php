<?php

namespace App\Http\Controllers\API\V1\Auth;

use App\Http\Controllers\Traits\HasPassportJsonResponse;
use <PERSON><PERSON>\Passport\Http\Controllers\AccessTokenController as PassportAccessTokenController;
use <PERSON>yholm\Psr7\Response as Psr7Response;
use Psr\Http\Message\ServerRequestInterface;

class AccessTokenController extends PassportAccessTokenController
{
    use HasPassportJsonResponse;

    public function issueToken(ServerRequestInterface $request)
    {
        return $this->withErrorHandling(function () use ($request) {
            return $this->responseJsonFromPassportResponse(
                $this->server->respondToAccessTokenRequest($request, new Psr7Response())
            );
        });
    }
}
