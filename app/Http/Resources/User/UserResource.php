<?php

namespace App\Http\Resources\User;

use App\Http\Resources\APIResourceBase;
use App\Models\LeadReport;
use App\Models\User;
use Illuminate\Http\Request;

/**
 * @class UserResource
 *
 * @property User $resource
 */
class UserResource extends APIResourceBase
{
    public function toArrayDefault(Request $request): array
    {
        $user = $this->resource;

        return [
            'id' => $user->getKey(),
            'account' => $user->account,
            'name' => $user->name,
            'phone_number' => $user->phone_number,
            'active_flag' => $user->active_flag,
            'roles' => $user->roles->map(function ($role) {
                return [
                    'id' => $role->getKey(),
                    'name' => $role->name,
                    'display_name' => $role->display_name,
                ];
            })->toArray(),
            'positions' => $user->positions->map(function ($position) {
                return [
                    'id' => $position->getKey(),
                    'name' => $position->name,
                ];
            })->toArray(),
            'shops' => $user->shops->map(function ($shop) {
                return [
                    'id' => $shop->getKey(),
                    'name' => $shop->name,
                    'region' => $shop->region,
                ];
            })->toArray(),
            'avatar' => $user->avatar?->only([
                'id',
                'name',
                'path',
                'url',
                'url_thumbnail',
            ]),
            'is_manager' => $user->managers()->whereNull('user_managers.deleted_at')->exists(),
            'menus' => [
                'lead_report_search' => $user->can('viewReport', LeadReport::class),
                'lead_report_search_sk' => (bool)$user->setting?->lead_report_sk,
                'lead_report_search_sk_all' => (bool)$user->setting?->lead_report_sk_all,
                'lead_report_search_ta' => (bool)$user->setting?->lead_report_ta,
                'lead_report_search_ta_all' => (bool)$user->setting?->lead_report_ta_all,
                'view_any_chat_message' => (bool)$user->setting?->view_any_chat_message,
            ],
            'created_at' => $user->created_at,
            'updated_at' => $user->updated_at,
            'deleted_at' => $user->deleted_at,
        ];
    }
}
