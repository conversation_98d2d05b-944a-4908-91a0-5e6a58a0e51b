/* Modern Input Styles */
.input-group {
    position: relative;
    margin-bottom: 1.5rem;
    width: 100%;
}

.input-wrapper {
    position: relative;
    width: 100%;
}

.modern-input {
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.5;
    color: #2d3748;
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    transition: all 0.3s ease;
    outline: none;
}

.modern-input:focus {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
}

.modern-input:hover {
    border-color: #cbd5e0;
}

.floating-label {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #fff;
    padding: 0 4px;
    color: #718096;
    font-size: 14px;
    transition: all 0.2s ease;
    pointer-events: none;
}

.modern-input:focus + .floating-label,
.modern-input:not(:placeholder-shown) + .floating-label {
    top: 0;
    transform: translateY(-50%) scale(0.85);
    color: #4299e1;
}

.input-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    transition: color 0.3s ease;
}

.modern-input:focus ~ .input-icon {
    color: #4299e1;
}

.input-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #4299e1;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.modern-input:focus ~ .input-border {
    transform: scaleX(1);
}

/* Error State */
.input-group.error .modern-input {
    border-color: #f56565;
}

.input-group.error .floating-label {
    color: #f56565;
}

.input-group.error .input-icon {
    color: #f56565;
}

.error-message {
    display: flex;
    align-items: center;
    margin-top: 4px;
    color: #f56565;
    font-size: 12px;
}

.error-message i {
    margin-right: 4px;
}

/* Password Toggle Button */
.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    padding: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #4299e1;
}

/* Checkbox Styles */
.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.checkbox-container input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkmark {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    background-color: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.checkbox-container:hover input ~ .checkmark {
    border-color: #4299e1;
}

.checkbox-container input:checked ~ .checkmark {
    background-color: #4299e1;
    border-color: #4299e1;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 5px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-text {
    font-size: 14px;
    color: #4a5568;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.forgot-password {
    color: #4299e1;
    font-size: 14px;
    text-decoration: none;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #2b6cb0;
    text-decoration: underline;
}

/* Submit Button */
.login-btn {
    position: relative;
    width: 100%;
    padding: 12px;
    background-color: #4299e1;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
}

.login-btn:hover {
    background-color: #2b6cb0;
    transform: translateY(-1px);
}

.login-btn:active {
    transform: translateY(0);
}

.btn-text {
    position: relative;
    z-index: 1;
}

.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
}

.login-btn.loading .btn-text {
    visibility: hidden;
}

.login-btn.loading .btn-loader {
    display: block;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #fff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.btn-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.login-btn:hover .btn-icon {
    transform: translate(4px, -50%);
} 