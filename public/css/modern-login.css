/* Modern Login Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.modern-login-page {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

/* Main Container */
.login-container {
    display: flex;
    height: 100vh;
    position: relative;
    z-index: 2;
}

/* Left Side - Login Form */
.login-left {
    flex: 1;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
}

.brand-section {
    text-align: center;
    z-index: 3;
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 32px;
    padding: 3rem 2.5rem;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.brand-logo {
    margin-bottom: 2.5rem;
    animation: fadeInUp 1s ease-out;
    position: relative;
}

.brand-logo::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.brand-logo:hover::before {
    opacity: 1;
}

.logo-img {
    width: 160px;
    height: auto;
    /* Giữ nguyên màu logo - không filter */
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 24px;
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    backdrop-filter: blur(20px);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo-img:hover {
    transform: scale(1.08) rotate(3deg);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.brand-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #ffffff, #f0f4ff, #ffffff);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeInUp 1s ease-out 0.2s both, shimmer 4s ease-in-out infinite;
    text-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
    letter-spacing: -1px;
    line-height: 1.1;
}

.brand-slogan {
    margin-top: 1.5rem;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.slogan-main {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.3px;
}

.slogan-sub {
    font-size: 1rem;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    letter-spacing: 0.2px;
}

/* Visual Elements */
.visual-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 24px;
    padding: 1.8rem;
    display: flex;
    align-items: center;
    gap: 1.2rem;
    animation: float 8s ease-in-out infinite;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    max-width: 280px;
}

.card-icon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.card-icon i {
    font-size: 1.6rem;
    color: #fff;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.card-content h4 {
    font-size: 1.05rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 0.4rem;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.2px;
}

.card-content p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.85);
    margin: 0;
    line-height: 1.4;
    letter-spacing: 0.1px;
}

.card-1 {
    top: 15%;
    left: 8%;
    animation-delay: 0s;
    transform: rotate(-5deg);
}

.card-2 {
    top: 45%;
    right: 10%;
    animation-delay: 2.5s;
    transform: rotate(3deg);
}

.card-3 {
    bottom: 20%;
    left: 15%;
    animation-delay: 5s;
    transform: rotate(-2deg);
}

/* Background Pattern */
.background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 60%),
        radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.06) 0%, transparent 60%),
        radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.04) 0%, transparent 50%);
    animation: patternMove 25s linear infinite;
    opacity: 0.7;
}

/* Right Side - Branding */
.login-right {
    flex: 1;
    background:
        linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    color: white;
    padding: 3rem;
}

.login-form-container {
    width: 100%;
    max-width: 400px;
    animation: slideInRight 1s ease-out;
}

.login-header {
    text-align: center;
    margin-bottom: 3rem;
}

.login-title {
    font-size: 2.8rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.8rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.5px;
    line-height: 1.2;
}

.login-subtitle {
    font-size: 1.15rem;
    color: #718096;
    font-weight: 500;
    letter-spacing: 0.2px;
    line-height: 1.4;
}

/* Welcome Icon */
.welcome-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    animation: pulse 2s infinite;
}

.welcome-icon i {
    font-size: 2rem;
    color: white;
}

/* Modern Form */
.modern-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.input-group {
    position: relative;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.modern-input {
    width: 100%;
    padding: 1.8rem 1.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 20px;
    font-size: 1.1rem;
    font-weight: 500;
    background: #ffffff;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    outline: none;
    position: relative;
    z-index: 1;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.modern-input:focus {
    border-color: #667eea;
    background: #ffffff;
    box-shadow:
        0 8px 30px rgba(102, 126, 234, 0.12),
        0 0 0 1px rgba(102, 126, 234, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: translateY(-1px) scale(1.01);
}

.modern-input:focus + .floating-label,
.modern-input:not(:placeholder-shown) + .floating-label {
    transform: translateY(-3.2rem) translateX(0.5rem) scale(0.85);
    color: #667eea;
    background: #ffffff;
    box-shadow: 0 2px 12px rgba(102, 126, 234, 0.15);
}

.modern-input:focus + .floating-label i,
.modern-input:not(:placeholder-shown) + .floating-label i {
    color: #667eea;
    transform: scale(1.1);
}

.modern-input:focus + .floating-label span,
.modern-input:not(:placeholder-shown) + .floating-label span {
    color: #667eea;
    font-weight: 600;
}

.floating-label {
    position: absolute;
    left: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    color: #718096;
    font-size: 1.1rem;
    font-weight: 500;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: #ffffff;
    padding: 0.3rem 0.8rem;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 0.6rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.floating-label i {
    font-size: 1.1rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    color: #a0aec0;
}

.floating-label span {
    font-weight: 600;
    letter-spacing: 0.3px;
}

.modern-input:focus ~ .floating-label {
    background: #ffffff;
}

.input-focus-border {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 0 0 20px 20px;
    transform: translateX(-50%);
    animation: gradientShift 2s ease-in-out infinite;
}

.modern-input:focus ~ .input-focus-border {
    width: 100%;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.password-toggle {
    position: absolute;
    right: 1.5rem;
    background: rgba(102, 126, 234, 0.1);
    border: none;
    color: #718096;
    cursor: pointer;
    font-size: 1.1rem;
    z-index: 2;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.15);
    transform: scale(1.05);
}

.password-toggle:active {
    transform: scale(0.95);
}

/* Error States */
.input-group.error .modern-input {
    border-color: #e53e3e;
    background: #fed7d7;
}

.input-group.error .input-icon {
    color: #e53e3e;
}

.error-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #e53e3e;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    animation: shake 0.5s ease-in-out;
}



/* Login Button */
.login-btn {
    width: 100%;
    padding: 1.4rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 18px;
    color: white;
    font-size: 1.15rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.login-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 15px 35px rgba(102, 126, 234, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.login-btn:active {
    transform: translateY(-1px) scale(1.01);
    transition: all 0.1s ease;
}

.btn-text {
    transition: opacity 0.3s ease;
}

.btn-loader {
    display: none;
    position: absolute;
}

.login-btn.loading .btn-text {
    opacity: 0;
}

.login-btn.loading .btn-loader {
    display: block;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.btn-icon {
    transition: transform 0.3s ease;
}

.login-btn:hover .btn-icon {
    transform: translateX(4px);
}

/* Footer */
.login-footer {
    text-align: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.login-footer p {
    color: #a0aec0;
    font-size: 0.875rem;
}

/* Background Animation */
.bg-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    animation: particleFloat 15s linear infinite;
}

.particle:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    left: 30%;
    animation-delay: 3s;
}

.particle:nth-child(3) {
    left: 50%;
    animation-delay: 6s;
}

.particle:nth-child(4) {
    left: 70%;
    animation-delay: 9s;
}

.particle:nth-child(5) {
    left: 90%;
    animation-delay: 12s;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes patternMove {
    0% {
        transform: translateX(0) translateY(0);
    }
    25% {
        transform: translateX(-10px) translateY(-10px);
    }
    50% {
        transform: translateX(10px) translateY(-20px);
    }
    75% {
        transform: translateX(-5px) translateY(-10px);
    }
    100% {
        transform: translateX(0) translateY(0);
    }
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.loading-content {
    text-align: center;
    color: #667eea;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* Success Overlay */
.success-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(102, 126, 234, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.success-content {
    text-align: center;
    color: white;
}

.success-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    animation: successPulse 1s ease-out;
}

.success-icon i {
    font-size: 2rem;
}

.success-content h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.success-content p {
    opacity: 0.9;
}

/* Form Shake Animation */
.modern-form.shake {
    animation: shake 0.5s ease-in-out;
}

/* Additional Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes successPulse {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Animate In Classes */
.animate-in {
    animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus States */
.input-wrapper.focused .input-icon {
    color: #667eea;
    transform: scale(1.1);
}

.input-wrapper.focused .modern-input {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Hover Effects */
.floating-card:hover {
    transform: translateY(-8px) scale(1.05) rotate(0deg) !important;
    box-shadow:
        0 20px 50px rgba(0, 0, 0, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.25);
}

.floating-card:hover .card-icon {
    transform: scale(1.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
}

.floating-card:hover .card-icon i {
    transform: scale(1.1);
}

.brand-logo:hover .logo-img {
    transform: scale(1.08) rotate(3deg);
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        flex-direction: column;
    }

    .login-left {
        flex: 1;
        padding: 1.5rem;
    }

    .login-right {
        flex: 0 0 45%;
        padding: 2rem 1.5rem;
    }

    .brand-section {
        padding: 2rem 1.5rem;
        border-radius: 24px;
    }

    .brand-title {
        font-size: 2.2rem;
    }

    .slogan-main {
        font-size: 1.1rem;
    }

    .slogan-sub {
        font-size: 0.9rem;
    }

    .floating-card {
        display: none;
    }

    .login-title {
        font-size: 2.2rem;
    }

    .modern-form {
        gap: 1.5rem;
    }

    .logo-img {
        width: 120px;
    }
}

@media (max-width: 480px) {
    .login-left {
        flex: 0 0 30%;
    }

    .brand-title {
        font-size: 1.5rem;
    }

    .login-title {
        font-size: 1.75rem;
    }

    .modern-input {
        padding: 1rem 1rem 1rem 2.5rem;
    }

    .floating-label {
        left: 2.5rem;
    }

    .input-icon {
        left: 0.75rem;
    }

    .login-form-container {
        max-width: 100%;
    }

    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}

/* Dark mode support - chỉ áp dụng cho phần cần thiết */
@media (prefers-color-scheme: dark) {
    /* Không áp dụng dark mode cho login form để tránh xung đột */
    .login-left {
        background: #ffffff !important;
    }

    .login-left .login-title {
        color: #1a202c !important;
    }

    .login-left .login-subtitle {
        color: #718096 !important;
    }

    .login-left .modern-input {
        background: #f8fafc !important;
        border-color: #e2e8f0 !important;
        color: #1a202c !important;
    }

    .login-left .floating-label {
        background: #f8fafc !important;
        color: #a0aec0 !important;
    }

    .login-left .login-footer p {
        color: #a0aec0 !important;
    }
}
