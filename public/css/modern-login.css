/* Modern Login Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.modern-login-page {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

/* Main Container */
.login-container {
    display: flex;
    height: 100vh;
    position: relative;
    z-index: 2;
}

/* Left Side - Login Form */
.login-left {
    flex: 1;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
}

.brand-section {
    text-align: center;
    z-index: 3;
    position: relative;
}

.brand-logo {
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease-out;
}

.logo-img {
    width: 120px;
    height: auto;
    /* Giữ nguyên màu logo - không filter */
    transition: transform 0.3s ease;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    backdrop-filter: blur(10px);
}

.logo-img:hover {
    transform: scale(1.05) rotate(2deg);
}

.brand-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.brand-slogan {
    margin-top: 1.5rem;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.slogan-main {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #fff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.slogan-sub {
    font-size: 1rem;
    font-weight: 300;
    opacity: 0.9;
    line-height: 1.5;
}

/* Visual Elements */
.visual-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: float 6s ease-in-out infinite;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.card-icon i {
    font-size: 1.5rem;
    color: #fff;
}

.card-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 0.25rem;
}

.card-content p {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 50%;
    right: 15%;
    animation-delay: 2s;
}

.card-3 {
    bottom: 25%;
    left: 20%;
    animation-delay: 4s;
}

/* Background Pattern */
.background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: patternMove 20s linear infinite;
}

/* Right Side - Branding */
.login-right {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    color: white;
    padding: 2rem;
}

.login-form-container {
    width: 100%;
    max-width: 400px;
    animation: slideInRight 1s ease-out;
}

.login-header {
    text-align: center;
    margin-bottom: 3rem;
}

.login-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-subtitle {
    font-size: 1rem;
    color: #718096;
    font-weight: 400;
}

/* Welcome Icon */
.welcome-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    animation: pulse 2s infinite;
}

.welcome-icon i {
    font-size: 2rem;
    color: white;
}

/* Modern Form */
.modern-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.input-group {
    position: relative;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.modern-input {
    width: 100%;
    padding: 1.5rem 1.2rem;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    font-size: 1rem;
    background: #f8fafc;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    position: relative;
    z-index: 1;
}

.modern-input:focus {
    border-color: #667eea;
    background: #ffffff;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.modern-input:focus + .floating-label,
.modern-input:not(:placeholder-shown) + .floating-label {
    transform: translateY(-3.2rem) translateX(0.5rem) scale(0.85);
    color: #667eea;
}

.modern-input:focus + .floating-label i,
.modern-input:not(:placeholder-shown) + .floating-label i {
    color: #667eea;
    transform: scale(1.1);
}

.floating-label {
    position: absolute;
    left: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    font-size: 1rem;
    pointer-events: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: #f8fafc;
    padding: 0 0.5rem;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-radius: 8px;
}

.floating-label i {
    font-size: 1rem;
    transition: all 0.3s ease;
}

.floating-label span {
    font-weight: 500;
}

.modern-input:focus ~ .floating-label {
    background: #ffffff;
}

.input-focus-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0 0 16px 16px;
}

.modern-input:focus ~ .input-focus-border {
    width: 100%;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    font-size: 1.1rem;
    z-index: 2;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #667eea;
}

/* Error States */
.input-group.error .modern-input {
    border-color: #e53e3e;
    background: #fed7d7;
}

.input-group.error .input-icon {
    color: #e53e3e;
}

.error-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #e53e3e;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    animation: shake 0.5s ease-in-out;
}



/* Login Button */
.login-btn {
    width: 100%;
    padding: 1.2rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.btn-text {
    transition: opacity 0.3s ease;
}

.btn-loader {
    display: none;
    position: absolute;
}

.login-btn.loading .btn-text {
    opacity: 0;
}

.login-btn.loading .btn-loader {
    display: block;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.btn-icon {
    transition: transform 0.3s ease;
}

.login-btn:hover .btn-icon {
    transform: translateX(4px);
}

/* Footer */
.login-footer {
    text-align: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.login-footer p {
    color: #a0aec0;
    font-size: 0.875rem;
}

/* Background Animation */
.bg-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    animation: particleFloat 15s linear infinite;
}

.particle:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    left: 30%;
    animation-delay: 3s;
}

.particle:nth-child(3) {
    left: 50%;
    animation-delay: 6s;
}

.particle:nth-child(4) {
    left: 70%;
    animation-delay: 9s;
}

.particle:nth-child(5) {
    left: 90%;
    animation-delay: 12s;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes patternMove {
    0% {
        transform: translateX(0) translateY(0);
    }
    25% {
        transform: translateX(-10px) translateY(-10px);
    }
    50% {
        transform: translateX(10px) translateY(-20px);
    }
    75% {
        transform: translateX(-5px) translateY(-10px);
    }
    100% {
        transform: translateX(0) translateY(0);
    }
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.loading-content {
    text-align: center;
    color: #667eea;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* Success Overlay */
.success-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(102, 126, 234, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.success-content {
    text-align: center;
    color: white;
}

.success-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    animation: successPulse 1s ease-out;
}

.success-icon i {
    font-size: 2rem;
}

.success-content h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.success-content p {
    opacity: 0.9;
}

/* Form Shake Animation */
.modern-form.shake {
    animation: shake 0.5s ease-in-out;
}

/* Additional Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes successPulse {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Animate In Classes */
.animate-in {
    animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus States */
.input-wrapper.focused .input-icon {
    color: #667eea;
    transform: scale(1.1);
}

.input-wrapper.focused .modern-input {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Hover Effects */
.floating-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
}

.brand-logo:hover .logo-img {
    transform: scale(1.05) rotate(5deg);
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        flex-direction: column;
    }

    .login-left {
        flex: 0 0 40%;
        padding: 1rem;
    }

    .brand-title {
        font-size: 2rem;
    }

    .brand-subtitle {
        font-size: 1rem;
    }

    .floating-card {
        display: none;
    }

    .login-right {
        flex: 1;
        padding: 1rem;
    }

    .login-title {
        font-size: 2rem;
    }

    .modern-form {
        gap: 1.5rem;
    }
}

@media (max-width: 480px) {
    .login-left {
        flex: 0 0 30%;
    }

    .brand-title {
        font-size: 1.5rem;
    }

    .login-title {
        font-size: 1.75rem;
    }

    .modern-input {
        padding: 1rem 1rem 1rem 2.5rem;
    }

    .floating-label {
        left: 2.5rem;
    }

    .input-icon {
        left: 0.75rem;
    }

    .login-form-container {
        max-width: 100%;
    }

    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}

/* Dark mode support - chỉ áp dụng cho phần cần thiết */
@media (prefers-color-scheme: dark) {
    /* Không áp dụng dark mode cho login form để tránh xung đột */
    .login-left {
        background: #ffffff !important;
    }

    .login-left .login-title {
        color: #1a202c !important;
    }

    .login-left .login-subtitle {
        color: #718096 !important;
    }

    .login-left .modern-input {
        background: #f8fafc !important;
        border-color: #e2e8f0 !important;
        color: #1a202c !important;
    }

    .login-left .floating-label {
        background: #f8fafc !important;
        color: #a0aec0 !important;
    }

    .login-left .login-footer p {
        color: #a0aec0 !important;
    }
}
