// Modern Login JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeLoginForm();
    initializeAnimations();
    initializeFormValidation();
});

// Initialize login form functionality
function initializeLoginForm() {
    const form = document.querySelector('.modern-form');
    const inputs = document.querySelectorAll('.modern-input');
    const loginBtn = document.querySelector('.login-btn');
    
    // Handle input focus/blur effects
    inputs.forEach(input => {
        // Set initial state for inputs with values
        if (input.value) {
            input.classList.add('has-value');
        }
        
        input.addEventListener('input', function() {
            if (this.value) {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
            
            // Clear error state when user starts typing
            const inputGroup = this.closest('.input-group');
            if (inputGroup.classList.contains('error')) {
                inputGroup.classList.remove('error');
            }
        });
        
        input.addEventListener('focus', function() {
            this.closest('.input-wrapper').classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.closest('.input-wrapper').classList.remove('focused');
        });
    });
    
    // Handle form submission
    if (form) {
        form.addEventListener('submit', function(e) {
            // Add loading state to button
            loginBtn.classList.add('loading');
            loginBtn.disabled = true;
            
            // Add a small delay to show the loading animation
            setTimeout(() => {
                // The form will submit normally after this
            }, 500);
        });
    }
}

// Toggle password visibility
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const passwordEye = document.getElementById('password-eye');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordEye.classList.remove('fa-eye');
        passwordEye.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        passwordEye.classList.remove('fa-eye-slash');
        passwordEye.classList.add('fa-eye');
    }
}

// Initialize animations
function initializeAnimations() {
    // Animate floating cards
    animateFloatingCards();
    
    // Add parallax effect to background
    addParallaxEffect();
    
    // Add typing animation to brand subtitle
    addTypingAnimation();
}

// Animate floating cards
function animateFloatingCards() {
    const cards = document.querySelectorAll('.floating-card');
    
    cards.forEach((card, index) => {
        // Add random movement
        setInterval(() => {
            const randomX = Math.random() * 20 - 10; // -10 to 10
            const randomY = Math.random() * 20 - 10; // -10 to 10
            
            card.style.transform = `translate(${randomX}px, ${randomY}px)`;
        }, 3000 + index * 1000);
        
        // Add hover effect
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05) translateZ(0)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) translateZ(0)';
        });
    });
}

// Add parallax effect
function addParallaxEffect() {
    const loginLeft = document.querySelector('.login-left');
    const backgroundPattern = document.querySelector('.background-pattern');
    
    if (loginLeft && backgroundPattern) {
        loginLeft.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = (e.clientX - rect.left) / rect.width;
            const y = (e.clientY - rect.top) / rect.height;
            
            const moveX = (x - 0.5) * 20;
            const moveY = (y - 0.5) * 20;
            
            backgroundPattern.style.transform = `translate(${moveX}px, ${moveY}px)`;
        });
        
        loginLeft.addEventListener('mouseleave', function() {
            backgroundPattern.style.transform = 'translate(0, 0)';
        });
    }
}

// Add typing animation to subtitle
function addTypingAnimation() {
    const subtitle = document.querySelector('.brand-subtitle');
    if (!subtitle) return;
    
    const text = subtitle.textContent;
    subtitle.textContent = '';
    
    let i = 0;
    const typeWriter = () => {
        if (i < text.length) {
            subtitle.textContent += text.charAt(i);
            i++;
            setTimeout(typeWriter, 100);
        }
    };
    
    // Start typing animation after a delay
    setTimeout(typeWriter, 1000);
}

// Form validation
function initializeFormValidation() {
    const form = document.querySelector('.modern-form');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Validate account field
        const accountInput = document.getElementById('account');
        const accountGroup = accountInput.closest('.input-group');
        
        if (!accountInput.value.trim()) {
            showError(accountGroup, 'Vui lòng nhập tài khoản');
            isValid = false;
        }
        
        // Validate password field
        const passwordInput = document.getElementById('password');
        const passwordGroup = passwordInput.closest('.input-group');
        
        if (!passwordInput.value.trim()) {
            showError(passwordGroup, 'Vui lòng nhập mật khẩu');
            isValid = false;
        } else if (passwordInput.value.length < 6) {
            showError(passwordGroup, 'Mật khẩu phải có ít nhất 6 ký tự');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            
            // Remove loading state from button
            const loginBtn = document.querySelector('.login-btn');
            loginBtn.classList.remove('loading');
            loginBtn.disabled = false;
            
            // Shake the form
            form.classList.add('shake');
            setTimeout(() => {
                form.classList.remove('shake');
            }, 500);
        }
    });
}

// Show error message
function showError(inputGroup, message) {
    inputGroup.classList.add('error');
    
    // Remove existing error message
    const existingError = inputGroup.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    
    // Add new error message
    const errorElement = document.createElement('span');
    errorElement.className = 'error-message';
    errorElement.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
    inputGroup.appendChild(errorElement);
}

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Enter key to submit form
    if (e.key === 'Enter' && e.target.classList.contains('modern-input')) {
        const form = document.querySelector('.modern-form');
        if (form) {
            form.requestSubmit();
        }
    }
    
    // Escape key to clear focus
    if (e.key === 'Escape') {
        document.activeElement.blur();
    }
});

// Add smooth scrolling for mobile
function addSmoothScrolling() {
    if (window.innerWidth <= 768) {
        const loginRight = document.querySelector('.login-right');
        if (loginRight) {
            loginRight.scrollIntoView({ behavior: 'smooth' });
        }
    }
}

// Handle window resize
window.addEventListener('resize', function() {
    // Recalculate animations on resize
    if (window.innerWidth <= 768) {
        // Disable complex animations on mobile
        const particles = document.querySelectorAll('.particle');
        particles.forEach(particle => {
            particle.style.display = 'none';
        });
    } else {
        const particles = document.querySelectorAll('.particle');
        particles.forEach(particle => {
            particle.style.display = 'block';
        });
    }
});

// Add loading screen effect
function showLoadingScreen() {
    const loadingScreen = document.createElement('div');
    loadingScreen.className = 'loading-screen';
    loadingScreen.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>Đang đăng nhập...</p>
        </div>
    `;
    
    document.body.appendChild(loadingScreen);
    
    // Remove loading screen after 3 seconds (or when page loads)
    setTimeout(() => {
        loadingScreen.remove();
    }, 3000);
}

// Add success animation
function showSuccessAnimation() {
    const successOverlay = document.createElement('div');
    successOverlay.className = 'success-overlay';
    successOverlay.innerHTML = `
        <div class="success-content">
            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>
            <h3>Đăng nhập thành công!</h3>
            <p>Đang chuyển hướng...</p>
        </div>
    `;
    
    document.body.appendChild(successOverlay);
    
    setTimeout(() => {
        successOverlay.remove();
    }, 2000);
}

// Initialize intersection observer for animations
function initializeIntersectionObserver() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, {
        threshold: 0.1
    });
    
    // Observe elements that should animate in
    const animateElements = document.querySelectorAll('.floating-card, .login-form-container');
    animateElements.forEach(el => observer.observe(el));
}

// Add touch gestures for mobile
function addTouchGestures() {
    let startY = 0;
    let currentY = 0;
    
    document.addEventListener('touchstart', function(e) {
        startY = e.touches[0].clientY;
    });
    
    document.addEventListener('touchmove', function(e) {
        currentY = e.touches[0].clientY;
        const diff = startY - currentY;
        
        // Add subtle parallax effect on scroll
        const backgroundPattern = document.querySelector('.background-pattern');
        if (backgroundPattern) {
            backgroundPattern.style.transform = `translateY(${diff * 0.1}px)`;
        }
    });
}

// Initialize all touch and mobile features
if ('ontouchstart' in window) {
    addTouchGestures();
}

// Initialize intersection observer
initializeIntersectionObserver();
