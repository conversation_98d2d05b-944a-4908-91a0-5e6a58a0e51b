[{"id": 1, "name": "viewAny booking", "display_name": "<PERSON>em danh s<PERSON>ch lịch hẹn", "group": "booking", "guard_name": "web"}, {"id": 2, "name": "create booking", "display_name": "<PERSON><PERSON><PERSON> l<PERSON> hẹn", "group": "booking", "guard_name": "web"}, {"id": 3, "name": "update booking", "display_name": "<PERSON><PERSON><PERSON> l<PERSON>ch hẹn", "group": "booking", "guard_name": "web"}, {"id": 4, "name": "viewAny booking_source", "display_name": "<PERSON><PERSON> danh s<PERSON>ch ng<PERSON>n", "group": "booking_source", "guard_name": "web"}, {"id": 5, "name": "create booking_source", "display_name": "<PERSON><PERSON><PERSON>", "group": "booking_source", "guard_name": "web"}, {"id": 6, "name": "update booking_source", "display_name": "<PERSON><PERSON><PERSON> ng<PERSON>", "group": "booking_source", "guard_name": "web"}, {"id": 7, "name": "delete booking_source", "display_name": "<PERSON><PERSON><PERSON> ng<PERSON>", "group": "booking_source", "guard_name": "web"}, {"id": 8, "name": "restore booking_source", "display_name": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> nguồn", "group": "booking_source", "guard_name": "web"}, {"id": 9, "name": "viewPhoneNumber customer", "display_name": "<PERSON><PERSON> số điện tho<PERSON><PERSON> h<PERSON> (full số)", "group": "customer", "guard_name": "web"}, {"id": 10, "name": "viewCustomerRevenues customer", "display_name": "<PERSON>em doanh thu từ khách hàng", "group": "customer", "guard_name": "web"}, {"id": 11, "name": "create customer_revenue", "display_name": "<PERSON><PERSON><PERSON> h<PERSON>a đơn", "group": "customer_revenue", "guard_name": "web"}, {"id": 12, "name": "viewAny shop", "display_name": "<PERSON><PERSON> danh s<PERSON>ch chi nh<PERSON>h", "group": "shop", "guard_name": "web"}, {"id": 13, "name": "create shop", "display_name": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "group": "shop", "guard_name": "web"}, {"id": 14, "name": "update shop", "display_name": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "group": "shop", "guard_name": "web"}, {"id": 15, "name": "delete shop", "display_name": "Xóa chi nh<PERSON>h", "group": "shop", "guard_name": "web"}, {"id": 16, "name": "restore shop", "display_name": "<PERSON><PERSON><PERSON><PERSON> phục chi nh<PERSON>h", "group": "shop", "guard_name": "web"}, {"id": 17, "name": "viewAny team", "display_name": "<PERSON>em da<PERSON> s<PERSON>ch team", "group": "team", "guard_name": "web"}, {"id": 18, "name": "create team", "display_name": "Tạo team", "group": "team", "guard_name": "web"}, {"id": 19, "name": "update team", "display_name": "Sửa team", "group": "team", "guard_name": "web"}, {"id": 20, "name": "delete team", "display_name": "Xóa team", "group": "team", "guard_name": "web"}, {"id": 21, "name": "restore team", "display_name": "Khôi phục team", "group": "team", "guard_name": "web"}, {"id": 22, "name": "viewAny user", "display_name": "<PERSON><PERSON> danh s<PERSON>ch nhân viên", "group": "user", "guard_name": "web"}, {"id": 23, "name": "create user", "display_name": "Tạo nhân viên", "group": "user", "guard_name": "web"}, {"id": 24, "name": "update user", "display_name": "<PERSON><PERSON><PERSON> nhân viên", "group": "user", "guard_name": "web"}, {"id": 25, "name": "delete user", "display_name": "Xóa nhân viên", "group": "user", "guard_name": "web"}, {"id": 26, "name": "import user", "display_name": "Import nhân viên", "group": "user", "guard_name": "web"}, {"id": 27, "name": "viewRevenue report", "display_name": "<PERSON><PERSON> báo c<PERSON>o doanh thu", "group": "report", "guard_name": "web"}, {"id": 28, "name": "viewAny product", "display_name": "<PERSON><PERSON> danh s<PERSON>ch d<PERSON>ch v<PERSON>", "group": "product", "guard_name": "web"}, {"id": 29, "name": "create product", "display_name": "<PERSON><PERSON><PERSON> vụ", "group": "product", "guard_name": "web"}, {"id": 30, "name": "update product", "display_name": "<PERSON><PERSON><PERSON> d<PERSON> vụ", "group": "product", "guard_name": "web"}, {"id": 31, "name": "delete product", "display_name": "<PERSON>ó<PERSON> v<PERSON>", "group": "product", "guard_name": "web"}, {"id": 32, "name": "restore product", "display_name": "<PERSON><PERSON>ô<PERSON> ph<PERSON><PERSON> d<PERSON> vụ", "group": "product", "guard_name": "web"}, {"id": 34, "name": "viewAny department", "display_name": "<PERSON><PERSON> danh s<PERSON>ch bộ phận", "group": "department", "guard_name": "web"}, {"id": 35, "name": "create department", "display_name": "<PERSON><PERSON><PERSON> bộ phận", "group": "department", "guard_name": "web"}, {"id": 36, "name": "update department", "display_name": "<PERSON><PERSON><PERSON> bộ phận", "group": "department", "guard_name": "web"}, {"id": 37, "name": "delete department", "display_name": "<PERSON><PERSON><PERSON> bộ phận", "group": "department", "guard_name": "web"}, {"id": 38, "name": "restore department", "display_name": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> bộ phận", "group": "department", "guard_name": "web"}, {"id": 42, "name": "viewAny customer_revenue", "display_name": "<PERSON><PERSON> danh s<PERSON>ch hóa đ<PERSON>n", "group": "customer_revenue", "guard_name": "web"}, {"id": 43, "name": "update customer_revenue", "display_name": "<PERSON><PERSON><PERSON> h<PERSON>a đơn", "group": "customer_revenue", "guard_name": "web"}, {"id": 44, "name": "delete customer_revenue", "display_name": "<PERSON><PERSON><PERSON> hóa đơn tại mọi thời điểm", "group": "customer_revenue", "guard_name": "web"}, {"id": 45, "name": "viewAny marketing_team", "display_name": "<PERSON>em danh sách team MKT", "group": "marketing_team", "guard_name": "web"}, {"id": 46, "name": "create marketing_team", "display_name": "Tạo team MKT", "group": "marketing_team", "guard_name": "web"}, {"id": 47, "name": "update marketing_team", "display_name": "Sửa team MKT", "group": "marketing_team", "guard_name": "web"}, {"id": 48, "name": "delete marketing_team", "display_name": "Xóa team MKT", "group": "marketing_team", "guard_name": "web"}, {"id": 49, "name": "restore marketing_team", "display_name": "Khôi phục team MKT", "group": "marketing_team", "guard_name": "web"}, {"id": 50, "name": "viewAny lead_report", "display_name": "<PERSON><PERSON> danh s<PERSON>ch b<PERSON>o cáo LEAD", "group": "lead_report", "guard_name": "web"}, {"id": 51, "name": "create lead_report", "display_name": "Tạo báo cáo LEAD", "group": "lead_report", "guard_name": "web"}, {"id": 52, "name": "update lead_report", "display_name": "Sửa báo cáo LEAD", "group": "lead_report", "guard_name": "web"}, {"id": 53, "name": "viewAny customer", "display_name": "<PERSON><PERSON> danh s<PERSON>ch kh<PERSON>ch hàng", "group": "customer", "guard_name": "web"}, {"id": 55, "name": "update customer", "display_name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hàng", "group": "customer", "guard_name": "web"}, {"id": 56, "name": "createReExamination booking", "display_name": "<PERSON><PERSON><PERSON> l<PERSON>ch tái k<PERSON>m", "group": "booking", "guard_name": "web"}, {"id": 57, "name": "viewAny doctor", "display_name": "<PERSON><PERSON> da<PERSON> s<PERSON>ch b<PERSON>c s<PERSON>", "group": "doctor", "guard_name": "web"}, {"id": 58, "name": "create doctor", "display_name": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "group": "doctor", "guard_name": "web"}, {"id": 59, "name": "update doctor", "display_name": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> s<PERSON>", "group": "doctor", "guard_name": "web"}, {"id": 60, "name": "delete doctor", "display_name": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "group": "doctor", "guard_name": "web"}, {"id": 61, "name": "restore doctor", "display_name": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> b<PERSON>c <PERSON>ĩ", "group": "doctor", "guard_name": "web"}, {"id": 62, "name": "viewAny lead_report_doctor", "display_name": "<PERSON><PERSON> danh s<PERSON>ch b<PERSON>o cáo LEAD b<PERSON>c sĩ", "group": "lead_report_doctor", "guard_name": "web"}, {"id": 63, "name": "create lead_report_doctor", "display_name": "Tạo báo cáo LEAD bác sĩ", "group": "lead_report_doctor", "guard_name": "web"}, {"id": 64, "name": "update lead_report_doctor", "display_name": "S<PERSON>a b<PERSON>o cáo LEAD b<PERSON>c sĩ", "group": "lead_report_doctor", "guard_name": "web"}, {"id": 65, "name": "create customer", "display_name": "<PERSON><PERSON><PERSON> h<PERSON>ng", "group": "customer", "guard_name": "web"}, {"id": 66, "name": "create fast_filter_lead_report", "display_name": "<PERSON><PERSON>o bộ lọc n<PERSON>h", "group": "fast_filter_lead_report", "guard_name": "web"}, {"id": 67, "name": "delete fast_filter_lead_report", "display_name": "<PERSON><PERSON><PERSON> bộ lọ<PERSON> n<PERSON>h", "group": "fast_filter_lead_report", "guard_name": "web"}, {"id": 68, "name": "updatePhoneNumber customer", "display_name": "<PERSON><PERSON><PERSON> số điện thoại", "group": "customer", "guard_name": "web"}, {"id": 69, "name": "viewAny staff_department", "display_name": "<PERSON><PERSON> danh s<PERSON>ch phòng ban", "group": "staff_department", "guard_name": "web"}, {"id": 70, "name": "create staff_department", "display_name": "<PERSON><PERSON><PERSON> phòng ban", "group": "staff_department", "guard_name": "web"}, {"id": 71, "name": "update staff_department", "display_name": "<PERSON><PERSON><PERSON> phòng ban", "group": "staff_department", "guard_name": "web"}, {"id": 72, "name": "delete staff_department", "display_name": "<PERSON>óa phòng ban", "group": "staff_department", "guard_name": "web"}, {"id": 73, "name": "viewAny company_address", "display_name": "<PERSON>em danh sách địa chỉ công ty", "group": "company_address", "guard_name": "web"}, {"id": 74, "name": "create company_address", "display_name": "<PERSON><PERSON><PERSON> địa chỉ công ty", "group": "company_address", "guard_name": "web"}, {"id": 75, "name": "update company_address", "display_name": "<PERSON><PERSON>a địa chỉ công ty", "group": "company_address", "guard_name": "web"}, {"id": 76, "name": "delete company_address", "display_name": "<PERSON><PERSON><PERSON> địa chỉ công ty", "group": "company_address", "guard_name": "web"}, {"id": 77, "name": "viewAny timesheet", "display_name": "<PERSON><PERSON> danh s<PERSON>ch bảng chấm công", "group": "timesheet", "guard_name": "web"}, {"id": 78, "name": "create timesheet", "display_name": "<PERSON><PERSON><PERSON> bảng chấm công", "group": "timesheet", "guard_name": "web"}, {"id": 79, "name": "update timesheet", "display_name": "<PERSON><PERSON><PERSON> bảng chấm công", "group": "timesheet", "guard_name": "web"}, {"id": 80, "name": "delete timesheet", "display_name": "<PERSON><PERSON><PERSON> bảng chấm công", "group": "timesheet", "guard_name": "web"}, {"id": 81, "name": "export timesheet", "display_name": "Export bảng chấm công", "group": "timesheet", "guard_name": "web"}, {"id": 82, "name": "import customer", "display_name": "Import kh<PERSON>ch hàng", "group": "customer", "guard_name": "web"}, {"id": 83, "name": "viewReportAll lead_report", "display_name": "<PERSON><PERSON> b<PERSON>o cáo LEAD tổng hợp", "group": "lead_report", "guard_name": "web"}, {"id": 84, "name": "delete booking", "display_name": "<PERSON><PERSON><PERSON> l<PERSON> hẹn", "group": "booking", "guard_name": "web"}, {"id": 85, "name": "activate user", "display_name": "On/off nhân viên", "group": "user", "guard_name": "web"}, {"id": 86, "name": "editReExamination booking", "display_name": "<PERSON><PERSON><PERSON> l<PERSON>ch tái k<PERSON>m", "group": "booking", "guard_name": "web"}, {"id": 87, "name": "viewAny shift", "display_name": "<PERSON><PERSON> danh s<PERSON>ch ca làm việc", "group": "shift", "guard_name": "web"}, {"id": 88, "name": "create shift", "display_name": "Tạo ca làm việc", "group": "shift", "guard_name": "web"}, {"id": 89, "name": "update shift", "display_name": "Sửa ca làm việc", "group": "shift", "guard_name": "web"}, {"id": 90, "name": "delete shift", "display_name": "Xóa ca làm việc", "group": "shift", "guard_name": "web"}, {"id": 92, "name": "viewAny position", "display_name": "<PERSON><PERSON> danh s<PERSON>ch ch<PERSON> v<PERSON>", "group": "position", "guard_name": "web"}, {"id": 93, "name": "create position", "display_name": "Tạ<PERSON> vụ", "group": "position", "guard_name": "web"}, {"id": 94, "name": "update position", "display_name": "<PERSON><PERSON><PERSON> vụ", "group": "position", "guard_name": "web"}, {"id": 95, "name": "delete position", "display_name": "<PERSON><PERSON><PERSON> v<PERSON>", "group": "position", "guard_name": "web"}, {"id": 96, "name": "viewReportDepartment lead_report", "display_name": "<PERSON><PERSON> b<PERSON>o cáo LEAD bộ phận", "group": "lead_report", "guard_name": "web"}, {"id": 97, "name": "viewReportProduct lead_report", "display_name": "<PERSON><PERSON> b<PERSON>o cáo LEAD dịch vụ", "group": "lead_report", "guard_name": "web"}, {"id": 99, "name": "viewReportDoctorAll lead_report", "display_name": "<PERSON><PERSON> b<PERSON>o cáo LEAD b<PERSON>c sĩ", "group": "lead_report", "guard_name": "web"}, {"id": 101, "name": "viewAny role", "display_name": "<PERSON><PERSON> danh s<PERSON>ch ph<PERSON> quyền", "group": "role", "guard_name": "web"}, {"id": 102, "name": "create role", "display_name": "<PERSON><PERSON><PERSON> phân quyền", "group": "role", "guard_name": "web"}, {"id": 103, "name": "update role", "display_name": "<PERSON><PERSON><PERSON> phân quyền", "group": "role", "guard_name": "web"}, {"id": 104, "name": "delete role", "display_name": "<PERSON><PERSON><PERSON> phân quyền", "group": "role", "guard_name": "web"}, {"id": 110, "name": "viewPhoneNumberFirstFiveHidden customer", "display_name": "<PERSON>em số điện tho<PERSON><PERSON> kh<PERSON> hà<PERSON> (che 5 số đầu)", "group": "customer", "guard_name": "web"}, {"id": 111, "name": "viewRevenueHeadEvaluation booking", "display_name": "<PERSON><PERSON> danh sách đ<PERSON>h giá đầu hóa đơn/tệ<PERSON> khách", "group": "booking", "guard_name": "web"}, {"id": 112, "name": "viewAny notice", "display_name": "<PERSON><PERSON> danh s<PERSON>ch thông báo", "group": "notice", "guard_name": "web"}, {"id": 113, "name": "create notice", "display_name": "<PERSON><PERSON><PERSON> thông báo", "group": "notice", "guard_name": "web"}, {"id": 114, "name": "update notice", "display_name": "<PERSON><PERSON><PERSON> thông báo", "group": "notice", "guard_name": "web"}, {"id": 115, "name": "delete notice", "display_name": "<PERSON><PERSON><PERSON> thông báo", "group": "notice", "guard_name": "web"}, {"id": 116, "name": "viewAny booking_history", "display_name": "<PERSON><PERSON> danh s<PERSON>ch lịch sử lịch hẹn", "group": "booking_history", "guard_name": "web"}, {"id": 117, "name": "create booking_history", "display_name": "<PERSON><PERSON><PERSON> lịch sử lịch hẹn", "group": "booking_history", "guard_name": "web"}, {"id": 120, "name": "viewAny customer_survey", "display_name": "<PERSON><PERSON> danh sách hiệu quả & thái độ", "group": "customer_survey", "guard_name": "web"}, {"id": 121, "name": "create customer_survey", "display_name": "<PERSON><PERSON><PERSON> hiệu quả & th<PERSON><PERSON> độ", "group": "customer_survey", "guard_name": "web"}, {"id": 122, "name": "update customer_survey", "display_name": "<PERSON><PERSON><PERSON> hiệu quả & th<PERSON><PERSON> độ", "group": "customer_survey", "guard_name": "web"}, {"id": 123, "name": "delete customer_survey", "display_name": "<PERSON><PERSON><PERSON> hiệu quả & th<PERSON><PERSON> độ", "group": "customer_survey", "guard_name": "web"}, {"id": 130, "name": "requireSpecificDoctors doctor", "display_name": "<PERSON><PERSON> giới hạn b<PERSON> (<PERSON><PERSON><PERSON> <PERSON><PERSON> thống)", "group": "doctor", "guard_name": "web"}, {"id": 131, "name": "exportBooking booking", "display_name": "Export lịch hẹn", "group": "booking", "guard_name": "web"}, {"id": 133, "name": "viewAny sale_report", "display_name": "<PERSON><PERSON> da<PERSON> s<PERSON>ch b<PERSON>o cáo SALE", "group": "sale_report", "guard_name": "web"}, {"id": 134, "name": "create sale_report", "display_name": "Tạo báo cáo SALE", "group": "sale_report", "guard_name": "web"}, {"id": 135, "name": "update sale_report", "display_name": "Sửa báo cáo SALE", "group": "sale_report", "guard_name": "web"}, {"id": 136, "name": "viewReport sale_report", "display_name": "<PERSON><PERSON> thống kê b<PERSON>o cáo SALE", "group": "sale_report", "guard_name": "web"}, {"id": 137, "name": "restrictAsSalesman sale_report", "display_name": "<PERSON>ị hạn chế như là một telesale (Chỉ tương tác với report đư<PERSON><PERSON> gán telesale chính là bản thân)", "group": "sale_report", "guard_name": "web"}, {"id": 138, "name": "requireSpecificDoctorsToBooking doctor", "display_name": "<PERSON>ị giới hạn bác sĩ với lịch hẹn", "group": "doctor", "guard_name": "web"}, {"id": 139, "name": "restrictAsSalesmanManager sale_report", "display_name": "Bị hạn chế như là một quản lý telesale (Chỉ tương tác với report đượ<PERSON> gán telesale là nhân viên mình quản lý)", "group": "sale_report", "guard_name": "web"}, {"id": 140, "name": "requireSpecificDoctorsToLeadReport doctor", "display_name": "Bị giới hạn bác sĩ với lead report", "group": "doctor", "guard_name": "web"}, {"id": 141, "name": "viewComparisonReportShop lead_report", "display_name": "<PERSON>em so s<PERSON>h báo cáo LEAD theo chi nhánh", "group": "lead_report", "guard_name": "web"}, {"id": 142, "name": "viewComparisonReportMarketingTeam lead_report", "display_name": "<PERSON><PERSON> so s<PERSON>h b<PERSON>o cáo LEAD theo team MTK", "group": "lead_report", "guard_name": "web"}, {"id": 143, "name": "viewComparisonReportBusinessDepartment lead_report", "display_name": "<PERSON>em so s<PERSON>h báo cáo LEAD theo phòng kinh doanh", "group": "lead_report", "guard_name": "web"}, {"id": 145, "name": "deleteIn24Hours customer_revenue", "display_name": "<PERSON>óa hóa đơn trong 24 giờ", "group": "customer_revenue", "guard_name": "web"}, {"id": 146, "name": "viewAny shop_kpi", "display_name": "<PERSON><PERSON> danh sách kpi chi nh<PERSON>h", "group": "shop_kpi", "guard_name": "web"}, {"id": 147, "name": "create shop_kpi", "display_name": "<PERSON><PERSON><PERSON> kpi chi nh<PERSON>h", "group": "shop_kpi", "guard_name": "web"}, {"id": 148, "name": "update shop_kpi", "display_name": "<PERSON><PERSON><PERSON> kpi chi nh<PERSON>h", "group": "shop_kpi", "guard_name": "web"}, {"id": 149, "name": "delete shop_kpi", "display_name": "Xóa kpi chi nh<PERSON>h", "group": "shop_kpi", "guard_name": "web"}, {"id": 150, "name": "viewAny marketing_team_kpi", "display_name": "<PERSON>em danh sách kpi team MKT", "group": "marketing_team_kpi", "guard_name": "web"}, {"id": 151, "name": "create marketing_team_kpi", "display_name": "Tạo kpi team MKT", "group": "marketing_team_kpi", "guard_name": "web"}, {"id": 152, "name": "update marketing_team_kpi", "display_name": "Sửa kpi team MKT", "group": "marketing_team_kpi", "guard_name": "web"}, {"id": 153, "name": "delete marketing_team_kpi", "display_name": "Xóa kpi team MKT", "group": "marketing_team_kpi", "guard_name": "web"}, {"id": 154, "name": "viewKpiChartShop kpi", "display_name": "<PERSON><PERSON> so s<PERSON>h <PERSON> chi nh<PERSON>h", "group": "kpi", "guard_name": "web"}, {"id": 155, "name": "viewKpiChartMarketingTeam kpi", "display_name": "<PERSON><PERSON> so sánh Kpi team MKT", "group": "kpi", "guard_name": "web"}, {"id": 157, "name": "listenBookingAudio booking", "display_name": "<PERSON>he file ghi âm", "group": "booking", "guard_name": "web"}, {"id": 158, "name": "setShowRealMoney user", "display_name": "On/off xem giá trị thực", "group": "user", "guard_name": "web"}, {"id": 159, "name": "viewReportLast2Months lead_report", "display_name": "<PERSON><PERSON> b<PERSON>o cáo LEAD 2 thán<PERSON> g<PERSON><PERSON> nh<PERSON>t", "group": "lead_report", "guard_name": "web"}, {"id": 160, "name": "viewCustomerRevenueLast2Months customer_revenue", "display_name": "<PERSON><PERSON> đơ<PERSON> 2 tháng gần nh<PERSON>t", "group": "customer_revenue", "guard_name": "web"}, {"id": 161, "name": "viewBookingLast2Months booking", "display_name": "<PERSON><PERSON> l<PERSON> hẹn 2 tháng gần nh<PERSON>t", "group": "booking", "guard_name": "web"}, {"id": 162, "name": "viewIdentityNumberOnlyLastFour customer", "display_name": "Chỉ xem 4 số cuối CCCD", "group": "customer", "guard_name": "web"}, {"id": 163, "name": "limitByMktTeam booking", "display_name": "Giới hạn theo team mkt", "group": "booking", "guard_name": "web"}, {"id": 164, "name": "limitByMktTeam customer_revenue", "display_name": "Giới hạn theo team mkt", "group": "customer_revenue", "guard_name": "web"}, {"id": 165, "name": "limitByMktTeam lead_report", "display_name": "Giới hạn theo team mkt", "group": "lead_report", "guard_name": "web"}, {"id": 166, "name": "limitByShop booking", "display_name": "<PERSON><PERSON><PERSON><PERSON> hạn theo chi nh<PERSON>h", "group": "booking", "guard_name": "web"}, {"id": 167, "name": "limitByShop customer", "display_name": "<PERSON><PERSON><PERSON><PERSON> hạn khách hàng theo chi nh<PERSON>h", "group": "customer", "guard_name": "web"}, {"id": 168, "name": "limitByShop customer_revenue", "display_name": "<PERSON><PERSON><PERSON><PERSON> hạn theo chi nh<PERSON>h", "group": "customer_revenue", "guard_name": "web"}, {"id": 169, "name": "limitByShop lead_report", "display_name": "<PERSON><PERSON><PERSON><PERSON> hạn theo chi nh<PERSON>h", "group": "lead_report", "guard_name": "web"}, {"id": 170, "name": "limitByShop customer_survey", "display_name": "<PERSON><PERSON><PERSON><PERSON> hạn theo chi nh<PERSON>h", "group": "customer_survey", "guard_name": "web"}, {"id": 171, "name": "viewAny marketing_team_filter", "display_name": "<PERSON><PERSON> danh s<PERSON>ch bộ lọc team MKT", "group": "marketing_team_filter", "guard_name": "web"}, {"id": 172, "name": "create marketing_team_filter", "display_name": "Tạo bộ lọc team MKT", "group": "marketing_team_filter", "guard_name": "web"}, {"id": 173, "name": "update marketing_team_filter", "display_name": "Sửa bộ lọc team MKT", "group": "marketing_team_filter", "guard_name": "web"}, {"id": 174, "name": "delete marketing_team_filter", "display_name": "Xóa bộ lọc team MKT", "group": "marketing_team_filter", "guard_name": "web"}, {"id": 176, "name": "viewAndCreateAndUpdateLimitByShop lead_report", "display_name": "Xem/tạo/sửa báo cáo LEAD giới hạn theo chi nh<PERSON>h", "group": "lead_report", "guard_name": "web"}, {"id": 177, "name": "manage shop_2_kpi", "display_name": "<PERSON><PERSON>ập <PERSON> do<PERSON>h thu, cpqc", "group": "shop_2_kpi", "guard_name": "web"}, {"id": 178, "name": "limitByDoctor customer_revenue", "display_name": "<PERSON><PERSON><PERSON><PERSON> hạn theo b<PERSON>c sĩ", "group": "customer_revenue", "guard_name": "web"}, {"id": 180, "name": "viewAny rating", "display_name": "<PERSON><PERSON> danh s<PERSON>ch đ<PERSON>h giá", "group": "rating", "guard_name": "web"}, {"id": 181, "name": "create rating", "display_name": "<PERSON><PERSON>o đ<PERSON> giá", "group": "rating", "guard_name": "web"}, {"id": 182, "name": "update rating", "display_name": "<PERSON><PERSON>a đ<PERSON>h giá", "group": "rating", "guard_name": "web"}, {"id": 183, "name": "delete rating", "display_name": "<PERSON>óa đ<PERSON> giá", "group": "rating", "guard_name": "web"}, {"id": 184, "name": "verify rating", "display_name": "<PERSON><PERSON><PERSON> th<PERSON>c đ<PERSON> giá", "group": "rating", "guard_name": "web"}, {"id": 185, "name": "updateIn24Hours rating", "display_name": "Sửa đánh giá trong 24 giờ", "group": "rating", "guard_name": "web"}, {"id": 186, "name": "deleteIn24Hours rating", "display_name": "<PERSON>óa đánh giá trong 24 giờ", "group": "rating", "guard_name": "web"}, {"id": 187, "name": "viewStatistics rating", "display_name": "<PERSON><PERSON> thống kê đánh giá", "group": "rating", "guard_name": "web"}, {"id": 191, "name": "viewAny shift_rule_history", "display_name": "<PERSON><PERSON> danh sách quy tắc ca làm việc", "group": "shift_rule_history", "guard_name": "web"}, {"id": 192, "name": "create shift_rule_history", "display_name": "<PERSON><PERSON><PERSON> quy tắc ca làm việc", "group": "shift_rule_history", "guard_name": "web"}, {"id": 193, "name": "update shift_rule_history", "display_name": "<PERSON><PERSON><PERSON> quy tắc ca làm việc", "group": "shift_rule_history", "guard_name": "web"}, {"id": 194, "name": "delete shift_rule_history", "display_name": "<PERSON><PERSON><PERSON> quy tắc ca làm việc", "group": "shift_rule_history", "guard_name": "web"}]